/* ===========
   Grid System - 網格系統樣式
   =========== */

/* 外層網格項目 */
.outer-grid-item {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    user-select: none;
    background-color: #0c2491;
    border-radius: 14px;
    text-align: center;
    overflow: hidden;
}

/* 內層網格 */
.inner-grid {
    grid-column: 2 / span 18;
    grid-row: 2 / span 10;
    display: grid;
    gap: 1px;
}

/* 網格項目 */
.grid-item {
    background-color: #292626;
    border: 1px solid #ffffff;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    user-select: none;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.grid-item:hover {
    outline: 5px solid rgba(0, 255, 255, 0.8);
    z-index: 900;
}

.grid-item.selected {
    background-color: #34ebd5;
    color: #333;
}

/* Grid Size Display Styles */
.grid-size {
    font-size: 14px;
    display: inline-block;
    margin-top: 10px;
}

/* 方形按鈕容器 */
.square-buttons-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 3px;
    width: 100%;
    flex: 1;
    padding: 5px;
    box-sizing: border-box;
}

/* 16格按鈕容器 */
.square-buttons-container-16 {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr 1fr;
    gap: 2px;
    width: 100%;
    flex: 1;
    padding: 4px;
    box-sizing: border-box;
    z-index: 5;
}

/* 當前在線人數樣式 */
.online-status-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
}

.online-status-button:hover {
    background: linear-gradient(135deg, #1e3c72, #354d88);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.online-status-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 5px;
    width: 100%;
}

.location-text {
    font-size: 14px;
    margin-bottom: 5px;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.online-count {
    font-size: 14px;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.count-number {
    font-weight: bold;
    color: #34ebd5;
    font-size: 16px;
    margin-top: 10px;
}
