/**
 * Service center functionality for the KMS Rental Grid application
 */

import { getElements, addEvent } from '../core/dom.js';
import { serviceContent } from '../core/config.js';
import { showServiceModal } from '../ui/modal.js';

/**
 * Initialize service center functionality
 */
export function initializeServices() {
    initializeServiceButtons();
}

/**
 * Initialize service buttons
 */
function initializeServiceButtons() {
    console.log('Initializing service buttons...');
    const serviceButtons = getElements('.service-button[data-service]');
    console.log('Found service buttons:', serviceButtons.length);

    serviceButtons.forEach((button, index) => {
        const serviceType = button.dataset.service;
        console.log(`Setting up button ${index + 1}: ${serviceType}`);

        addEvent(button, 'click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Service button clicked:', serviceType);
            showServiceModal(serviceType, serviceContent);
        });

        // Add visual feedback
        button.style.cursor = 'pointer';
        button.setAttribute('title', `Click to open ${serviceType} information`);
    });

    // Fallback: Add event delegation to parent container
    const serviceContainer = document.querySelector('.outer-grid-item[style*="grid-column: 1"][style*="grid-row: 12"]');
    if (serviceContainer) {
        console.log('Adding event delegation to service container');
        addEvent(serviceContainer, 'click', function(e) {
            const serviceButton = e.target.closest('.service-button[data-service]');
            if (serviceButton) {
                const serviceType = serviceButton.dataset.service;
                console.log('Service button clicked via delegation:', serviceType);
                showServiceModal(serviceType, serviceContent);
            }
        });
    }
}

/**
 * Handle feedback form submission
 * @param {HTMLFormElement} form - The feedback form
 */
export function handleFeedbackSubmission(form) {
    if (!form) return;
    
    addEvent(form, 'submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const feedbackData = {
            type: getFormValue(form, 'feedbackType'),
            subject: getFormValue(form, 'feedbackSubject'),
            content: getFormValue(form, 'feedbackContent'),
            email: getFormValue(form, 'contactEmail')
        };
        
        // This should be backend submission logic
        // In a real application, use fetch API or AJAX
        console.log('提交反饋:', feedbackData);
        
        // Show success message
        const successMsg = document.getElementById('feedbackSuccess');
        if (successMsg) {
            successMsg.style.display = 'block';
        }
        
        // Reset form
        form.reset();
        
        // Hide success message after 3 seconds
        setTimeout(() => {
            if (successMsg) {
                successMsg.style.display = 'none';
            }
        }, 3000);
    });
}

/**
 * Get form field value
 * @param {HTMLFormElement} form - The form
 * @param {string} fieldId - The field ID
 * @returns {string} - The field value
 */
function getFormValue(form, fieldId) {
    const field = form.querySelector(`#${fieldId}`);
    return field ? field.value : '';
}
