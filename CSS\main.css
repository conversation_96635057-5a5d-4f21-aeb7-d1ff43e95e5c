/* ===========
   KMS Rental Grid - Main CSS File
   主樣式文件 - 統一引用所有分類後的 CSS 文件
   =========== */

/*
 * 這個文件按功能性分類引用所有 CSS 模組
 * 便於後續語法的管理與搜索，提高開發效率
 * 
 * 文件結構說明：
 * 1. base.css - 基礎樣式（body、容器、搜尋列等）
 * 2. grid.css - 網格系統（grid-container、grid-item 等）
 * 3. buttons.css - 按鈕樣式（toggle-button、square-button 等）
 * 4. modals.css - 模態框樣式（modal-overlay、guide-modal 等）
 * 5. chat.css - 聊天系統（Live Chat 相關樣式）
 * 6. auth.css - 認證系統（登入註冊相關樣式）
 * 7. themes.css - 主題顏色（40種模式顏色和主題樣式）
 * 8. animations.css - 動畫效果（所有動畫和過渡效果）
 * 9. responsive.css - 響應式設計（媒體查詢和響應式樣式）
 */

/* 1. 基礎樣式 - 必須最先載入 */
@import url('base.css');

/* 2. 網格系統 - 核心布局系統 */
@import url('grid.css');

/* 3. 按鈕樣式 - 互動元素 */
@import url('buttons.css');

/* 4. 模態框樣式 - 彈窗和對話框 */
@import url('modals.css');

/* 5. 聊天系統 - Live Chat 功能 */
@import url('chat.css');

/* 6. 認證系統 - 登入註冊功能 */
@import url('auth.css');

/* 7. 主題顏色 - 顏色方案和主題 */
@import url('themes.css');

/* 8. 動畫效果 - 動畫和過渡效果 */
@import url('animations.css');

/* 9. 響應式設計 - 必須最後載入以覆蓋其他樣式 */
@import url('responsive.css');

/* ===========
   全域覆蓋樣式
   =========== */

/* 確保所有元素使用 border-box */
*, *::before, *::after {
    box-sizing: border-box;
}

/* 滾動條樣式統一 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(52, 235, 213, 0.6);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(52, 235, 213, 0.8);
}

/* Firefox 滾動條 */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(52, 235, 213, 0.6) rgba(0, 0, 0, 0.1);
}

/* 選擇文字顏色 */
::selection {
    background-color: rgba(52, 235, 213, 0.3);
    color: #333;
}

::-moz-selection {
    background-color: rgba(52, 235, 213, 0.3);
    color: #333;
}

/* 焦點樣式統一 */
:focus {
    outline: 2px solid rgba(52, 235, 213, 0.6);
    outline-offset: 2px;
}

/* 無障礙支持 - 隱藏元素但保持螢幕閱讀器可讀 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 工具類別 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.m-0 { margin: 0; }
.p-0 { padding: 0; }

.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }

.user-select-none { user-select: none; }

/* 列印樣式 */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .grid-container {
        background: white !important;
    }
    
    .grid-item, .outer-grid-item {
        background: white !important;
        border: 1px solid black !important;
        color: black !important;
    }
}

/* 開發模式樣式 - 僅在開發環境顯示 */
.dev-mode .debug-info {
    position: fixed;
    top: 10px;
    left: 10px;
    background: rgba(255, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 9999;
}

/* 效能優化 */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* 載入狀態 */
.loading {
    pointer-events: none;
    opacity: 0.6;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #34ebd5;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 錯誤狀態 */
.error {
    border-color: #e74c3c !important;
    background-color: rgba(231, 76, 60, 0.1) !important;
}

.error-message {
    color: #e74c3c;
    font-size: 14px;
    margin-top: 5px;
}

/* 成功狀態 */
.success {
    border-color: #27ae60 !important;
    background-color: rgba(39, 174, 96, 0.1) !important;
}

.success-message {
    color: #27ae60;
    font-size: 14px;
    margin-top: 5px;
}

/* 警告狀態 */
.warning {
    border-color: #f39c12 !important;
    background-color: rgba(243, 156, 18, 0.1) !important;
}

.warning-message {
    color: #f39c12;
    font-size: 14px;
    margin-top: 5px;
}

/* 版本資訊 */
/*
 * KMS Rental Grid CSS Framework
 * Version: 2.0.0
 * Last Updated: 2024-12-17
 * 
 * 重構說明：
 * - 將原本的 styles.css (2358行) 分解為 9 個功能性模組
 * - 提高代碼可維護性和可搜索性
 * - 優化載入效能和開發體驗
 * - 支援模組化開發和團隊協作
 */
