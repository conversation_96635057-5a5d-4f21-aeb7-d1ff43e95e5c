/* ===========
   Themes and Colors - 主題顏色樣式
   =========== */

/* 40 種模式顏色 */
.content-mode-1 .grid-item  { background: #ffb3ba; }
.content-mode-2 .grid-item  { background: #ffdfba; }
.content-mode-3 .grid-item  { background: #ffffba; }
.content-mode-4 .grid-item  { background: #baffc9; }
.content-mode-5 .grid-item  { background: #bae1ff; }
.content-mode-6 .grid-item  { background: #e0baff; }
.content-mode-7 .grid-item  { background: #ffbaf0; }
.content-mode-8 .grid-item  { background: #bab3ff; }
.content-mode-9 .grid-item  { background: #ffc3a0; }
.content-mode-10 .grid-item { background: #a0ffe0; }
.content-mode-11 .grid-item { background: #d4a5a5; }
.content-mode-12 .grid-item { background: #a5d4ae; }
.content-mode-13 .grid-item { background: #a5aed4; }
.content-mode-14 .grid-item { background: #d4a5d1; }
.content-mode-15 .grid-item { background: #d4d1a5; }
.content-mode-16 .grid-item { background: #a5d4d1; }
.content-mode-17 .grid-item { background: #d1d4a5; }
.content-mode-18 .grid-item { background: #a5a5d4; }
.content-mode-19 .grid-item { background: #ffa07a; }
.content-mode-20 .grid-item { background: #7affaa; }
.content-mode-21 .grid-item { background: #7aafff; }
.content-mode-22 .grid-item { background: #ff7a7a; }
.content-mode-23 .grid-item { background: #7aff7a; }
.content-mode-24 .grid-item { background: #7a7aff; }
.content-mode-25 .grid-item { background: #ffd700; }
.content-mode-26 .grid-item { background: #d7ff00; }
.content-mode-27 .grid-item { background: #00ffd7; }
.content-mode-28 .grid-item { background: #d700ff; }
.content-mode-29 .grid-item { background: #00d7ff; }
.content-mode-30 .grid-item { background: #ff00d7; }
.content-mode-31 .grid-item { background: #8a2be2; }
.content-mode-32 .grid-item { background: #2be28a; }
.content-mode-33 .grid-item { background: #e22b2b; }
.content-mode-34 .grid-item { background: #2b2be2; }
.content-mode-35 .grid-item { background: #e2e22b; }
.content-mode-36 .grid-item { background: #2be2e2; }
.content-mode-37 .grid-item { background: #e2b72b; }
.content-mode-38 .grid-item { background: #b72be2; }
.content-mode-39 .grid-item { background: #2be2b7; }
.content-mode-40 .grid-item { background: #b7e22b; }

/* 高級房地產彈窗樣式 */
.premium-content {
    width: 100%;
    color: #fff;
    text-align: left;
}

.premium-description {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 20px;
    color: #e0e0e0;
}

.premium-features {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.premium-features h4 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #34ebd5;
}

.premium-features ul {
    list-style-type: none;
    padding: 0;
}

.premium-features li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
    font-size: 16px;
}

.premium-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #34ebd5;
    font-weight: bold;
}

.premium-pricing {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px 20px;
    text-align: center;
}

.premium-price {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
}

.premium-btn {
    background: linear-gradient(to right, #34ebd5, #00bfff);
    color: #fff;
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: bold;
    margin-top: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 235, 213, 0.5);
}

/* App Store 彈窗樣式 */
.app-store-content {
    width: 100%;
    color: #fff;
    text-align: left;
}

.app-store-description {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 20px;
    color: #e0e0e0;
}

.app-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.app-category {
    flex: 1;
    min-width: 250px;
}

.app-category h4 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #34ebd5;
    background: rgba(0, 0, 0, 0.3);
    padding: 8px 15px;
    border-radius: 5px;
}

.app-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.app-item {
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 10px;
    transition: all 0.3s ease;
}

.app-item:hover {
    background: rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.app-icon {
    font-size: 32px;
    margin-right: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.app-info {
    flex: 1;
}

.app-name {
    font-weight: bold;
    margin-bottom: 3px;
}

.app-rating {
    font-size: 12px;
    color: #ffd700;
}

.app-download-btn {
    background: #34ebd5;
    color: #111;
    border: none;
    border-radius: 15px;
    padding: 5px 15px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.app-download-btn:hover {
    background: #2bc4b0;
    transform: scale(1.05);
}

.app-store-more-btn {
    background: linear-gradient(to right, #2193b0, #6dd5ed);
    color: #fff;
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: bold;
    margin-top: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
    width: 200px;
    margin: 20px auto 0;
    text-align: center;
}

.app-store-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(33, 147, 176, 0.5);
}

/* 反饋表單樣式 */
.feedback-form {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.feedback-form .form-group {
    margin-bottom: 20px;
}

.feedback-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.feedback-form .form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.2);
    color: #fff;
}

.feedback-form textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

.rating-container {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-container input {
    display: none;
}

.rating-container label {
    display: inline-block;
    cursor: pointer;
    width: 30px;
    font-size: 30px;
    color: #ccc;
    margin: 0;
    transition: color 0.2s;
}

.rating-container input:checked ~ label,
.rating-container label:hover,
.rating-container label:hover ~ label {
    color: #ffcc00;
}

.success-message {
    background-color: rgba(39, 174, 96, 0.2);
    color: #27ae60;
    padding: 15px;
    border-radius: 5px;
    text-align: center;
    margin-top: 20px;
    font-weight: bold;
}
