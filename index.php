<!DOCTYPE html>
<html lang="en">
    <head>
        <title>KMS Rental Grid - Virtual Real Estate Platform</title>

        <meta charset="UTF-8" />
        <meta name="robots" content="noarchive" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="author" content="Kelvin KMS" />
        <meta name="keywords" content="KempireS, Virtual Real Estate, Rental Grid, Property Management" />
        <meta name="description" content="KMS Rental Grid - The Ultimate Virtual Real Estate Platform by KempireS" />

        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="icon" type="image/png" sizes="16x16" href="/Favicon/KMS_Logo_16.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/Favicon/KMS_Logo_32.png" />
        <link rel="icon" type="image/png" sizes="96x96" href="/Favicon/KMS_Logo_96.png" />
        <link rel="icon" type="image/png" sizes="512x512" href="/Favicon/KMS_Logo_512.png" />
        <link rel="apple-touch-icon-precomposed" sizes="152x152" href="/Favicon/KMS_Logo_152.png" />
        <link rel="mask-icon" href="/Favicon/KMS_Logo.svg" color="#5bbad5">
        <link rel="manifest" href="/manifest.json">

        <!-- Import CSS files - 模組化 CSS 架構 -->
        <link rel="stylesheet" href="CSS/main.css">
    </head>
    <body>
        <div class="grid-container">
            <!-- Left Side Button Area -->
            <div class="outer-grid-item" style="grid-column: 1; grid-row: 2; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="1">1</button>
                <button class="toggle-button" data-mode="2">2</button>
                <button class="toggle-button" data-mode="3">3</button>
                <button class="toggle-button" data-mode="4">4</button>
            </div>

            <div class="outer-grid-item" style="grid-column: 1; grid-row: 3; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="5">5</button>
                <button class="toggle-button" data-mode="6">6</button>
                <button class="toggle-button" data-mode="7">7</button>
                <button class="toggle-button" data-mode="8">8</button>
            </div>

            <div class="outer-grid-item" style="grid-column: 1; grid-row: 4; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="9">9</button>
                <button class="toggle-button" data-mode="10">10</button>
                <button class="toggle-button" data-mode="11">11</button>
                <button class="toggle-button" data-mode="12">12</button>
            </div>

            <div class="outer-grid-item" style="grid-column: 1; grid-row: 5; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="13">13</button>
                <button class="toggle-button" data-mode="14">14</button>
                <button class="toggle-button" data-mode="15">15</button>
                <button class="toggle-button" data-mode="16">16</button>
            </div>

            <div class="outer-grid-item" style="grid-column: 1; grid-row: 6; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="17">17</button>
                <button class="toggle-button" data-mode="18">18</button>
                <button class="toggle-button" data-mode="19">19</button>
                <button class="toggle-button" data-mode="20">20</button>
            </div>

            <div class="outer-grid-item" style="grid-column: 1; grid-row: 7; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="21">21</button>
                <button class="toggle-button" data-mode="22">22</button>
                <button class="toggle-button" data-mode="23">23</button>
                <button class="toggle-button" data-mode="24">24</button>
            </div>

            <div class="outer-grid-item" style="grid-column: 1; grid-row: 8; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="25">25</button>
                <button class="toggle-button" data-mode="26">26</button>
                <button class="toggle-button" data-mode="27">27</button>
                <button class="toggle-button" data-mode="28">28</button>
            </div>

            <div class="outer-grid-item" style="grid-column: 1; grid-row: 9; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="29">29</button>
                <button class="toggle-button" data-mode="30">30</button>
                <button class="toggle-button" data-mode="31">31</button>
                <button class="toggle-button" data-mode="32">32</button>
            </div>

            <div class="outer-grid-item" style="grid-column: 1; grid-row: 10; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="33">33</button>
                <button class="toggle-button" data-mode="34">34</button>
                <button class="toggle-button" data-mode="35">35</button>
                <button class="toggle-button" data-mode="36">36</button>
            </div>

            <div class="outer-grid-item" style="grid-column: 1; grid-row: 11; flex-direction: column; gap: 2px;">
                <button class="toggle-button" data-mode="37">37</button>
                <button class="toggle-button" data-mode="38">38</button>
                <button class="toggle-button" data-mode="39">39</button>
                <button class="toggle-button" data-mode="40">40</button>
            </div>

            <!-- Top Button Area (20 slots) -->
            <div class="outer-grid-item column" style="grid-column: 1; grid-row: 1;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Property Level</div>
                    <div class="square-buttons-container-16" id="categoryButtonsContainer">
                        <button class="square-button-small active" data-category="A" id="catA">A</button>
                        <button class="square-button-small" data-category="B" id="catB">B</button>
                        <button class="square-button-small" data-category="C" id="catC">C</button>
                        <button class="square-button-small" data-category="D" id="catD">D</button>
                        <button class="square-button-small" data-category="E" id="catE">E</button>
                        <button class="square-button-small" data-category="F" id="catF">F</button>
                        <button class="square-button-small" data-category="G" id="catG">G</button>
                        <button class="square-button-small" data-category="H" id="catH">H</button>
                        <button class="square-button-small" data-category="I" id="catI">I</button>
                        <button class="square-button-small" data-category="J" id="catJ">J</button>
                        <button class="square-button-small" data-category="K" id="catK">K</button>
                        <button class="square-button-small" data-category="L" id="catL">L</button>
                        <button class="square-button-small" data-category="M" id="catM">M</button>
                        <button class="square-button-small" data-category="N" id="catN">N</button>
                        <button class="square-button-small" data-category="O" id="catO">O</button>
                        <button class="square-button-small" data-category="P" id="catP">P</button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 2; grid-row: 1;">
                <div class="toggle-button online-status-button">
                    <div class="toggle-button-label">Current Online Info</div>
                    <div class="online-status-container">
                        <div class="location-text" id="currentLocationText">Your location: A1</div>
                        <div class="online-count" id="onlineUserCount">Currently <span class="count-number">0</span> users online</div>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 3; grid-row: 1;">
                <div class="toggle-button toggle-button-3d">
                    <div class="toggle-button-label">Beauty Rankings</div>
                    <div class="topic-icon">👸</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 4; grid-row: 1;">
                <div class="toggle-button toggle-button-3d">
                    <div class="toggle-button-label">Hot Topics</div>
                    <div class="topic-icon">🔥</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 5; grid-row: 1;">
                <div class="toggle-button toggle-button-3d">
                    <div class="toggle-button-label">Entertainment</div>
                    <div class="topic-icon">🎭</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 6; grid-row: 1;">
                <div class="toggle-button toggle-button-3d">
                    <div class="toggle-button-label">Sports Events</div>
                    <div class="topic-icon">⚽</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 7; grid-row: 1;">
                <div class="toggle-button toggle-button-3d">
                    <div class="toggle-button-label">Financial News</div>
                    <div class="topic-icon">📈</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 8; grid-row: 1;">
                <div class="toggle-button toggle-button-3d">
                    <div class="toggle-button-label">Fashion Trends</div>
                    <div class="topic-icon">👗</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 9; grid-row: 1;">
                <div class="toggle-button toggle-button-3d">
                    <div class="toggle-button-label">Tech News</div>
                    <div class="topic-icon">📱</div>
                </div>
            </div>
            <div class="outer-grid-item column logo-container" style="grid-column: 10; grid-row: 1; grid-column-end: span 2;">
                <div class="toggle-button">
                    <div class="title-3d">KempireS</div>
                    <button class="help-button" id="helpButton" title="User Guide">
                        <span class="help-icon">❓</span>
                    </button>
                </div>
                <div class="search-container">
                    <div class="search-bar">
                        <input type="text" id="globalSearch" placeholder="Search users, properties or hot topics..." />
                        <button id="searchButton">
                            <span class="search-icon">🔍</span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 12; grid-row: 1;">
                <div class="toggle-button premium-button" data-premium="emperor">
                    <div class="toggle-button-label">Emperor Exclusive Properties</div>
                    <div class="premium-icon">👑</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 13; grid-row: 1;">
                <div class="toggle-button premium-button" data-premium="vip1">
                    <div class="toggle-button-label">Premium VIP Properties</div>
                    <div class="premium-icon">💎</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 14; grid-row: 1;">
                <div class="toggle-button premium-button" data-premium="vip2">
                    <div class="toggle-button-label">Premium VIP Properties</div>
                    <div class="premium-icon">💎</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 15; grid-row: 1;">
                <div class="toggle-button premium-button" data-premium="vip3">
                    <div class="toggle-button-label">Premium VIP Properties</div>
                    <div class="premium-icon">💎</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 16; grid-row: 1;">
                <div class="toggle-button premium-button" data-premium="vip4">
                    <div class="toggle-button-label">Premium VIP Properties</div>
                    <div class="premium-icon">💎</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 17; grid-row: 1;">
                <div class="toggle-button premium-button" data-premium="vip5">
                    <div class="toggle-button-label">Premium VIP Properties</div>
                    <div class="premium-icon">💎</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 18; grid-row: 1;">
                <div class="toggle-button premium-button" data-premium="vip6">
                    <div class="toggle-button-label">Premium VIP Properties</div>
                    <div class="premium-icon">💎</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 19; grid-row: 1;">
                <div class="toggle-button app-store-button" data-premium="appstore">
                    <div class="toggle-button-label">KMS App Store</div>
                    <div class="app-store-icon">🛒</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 1;">
                <div class="toggle-button">
                    <div class="toggle-button-label">User</div>
                    <div class="login-register-btns">
                        <button class="login-btn" id="loginBtn">Login</button>
                        <button class="register-btn" id="registerBtn">Register</button>
                    </div>
                </div>
            </div>

            <!-- Bottom Button Area (20 slots) -->
            <div class="outer-grid-item column" style="grid-column: 1; grid-row: 12; flex-direction: column; gap: 8px;">
                <div class="toggle-button service-button" data-service="customer-service">
                    <div class="toggle-button-label">Customer Service</div>
                </div>
                <div class="toggle-button service-button" data-service="terms">
                    <div class="toggle-button-label">Terms of Service</div>
                </div>
                <div class="toggle-button service-button" data-service="privacy">
                    <div class="toggle-button-label">Privacy Policy</div>
                </div>
                <div class="toggle-button service-button" data-service="feedback">
                    <div class="toggle-button-label">User Feedback</div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 5; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T5</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 6; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T6</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 7; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T7</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 8; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T8</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 9; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T9</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 10; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T10</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 11; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T11</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 12; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T12</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 13; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T13</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 14; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T14</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 15; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T15</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 16; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T16</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 17; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T17</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 18; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T18</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 19; grid-row: 12;">
                <div class="toggle-button">
                    <div class="toggle-button-label">T19</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 12;">
                <div class="toggle-button live-chat-button" id="liveChatButton">
                    <div class="toggle-button-label">Live Chat</div>
                    <div class="live-chat-icon-container">
                        <div class="live-chat-icon">💬</div>
                        <div class="live-chat-status">Online Support</div>
                    </div>
                </div>
            </div>

            <!-- Right Side Button Area (10 slots) -->
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 2;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Property Management</div>
                    <div class="square-buttons-container">
                        <button class="square-button">Rent</button>
                        <button class="square-button">Buy</button>
                        <button class="square-button">Sell</button>
                        <button class="square-button">Search</button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 3;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Upgrade House Size</div>
                    <div class="house-upgrade-btn">
                        <div class="upgrade-icon">🏠→🏘️</div>
                        <div class="upgrade-text">Click to Upgrade</div>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 4;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Room Management</div>
                    <div class="square-buttons-container">
                        <button class="square-button">Rent</button>
                        <button class="square-button">Auction</button>
                        <button class="square-button">Add</button>
                        <button class="square-button">Count</button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 5;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Profile Editor</div>
                    <div class="profile-edit-btn">
                        <div class="profile-icon">👤</div>
                        <div class="profile-text">Edit Profile</div>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 6;">
                <div class="toggle-button">
                    <div class="toggle-button-label">House Structure Editor</div>
                    <div class="house-struct-btn">
                        <div class="struct-icon">🏗️</div>
                        <div class="struct-text">Adjust Structure</div>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 7;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Random Button 1</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 8;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Random Button 2</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 9;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Random Button 3</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 10;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Random Button 4</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>
            <div class="outer-grid-item column" style="grid-column: 20; grid-row: 11;">
                <div class="toggle-button">
                    <div class="toggle-button-label">Random Button 5</div>
                    <div class="square-buttons-container">
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                        <button class="square-button random-text"></button>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="inner-grid" id="innerGrid" style="grid-column: 2 / span 18; grid-row: 2 / span 10;"></div>

            <!-- Modal Structure -->
            <div class="modal-overlay" id="modalOverlay">
                <div class="win-modal">
                    <div class="titlebar">
                        <span>Property Details</span>
                        <button class="close-btn" onclick="closeModal()">×</button>
                    </div>
                    <div class="modal-content" id="modalContent">
                        <img src="" class="modal-image" alt="Preview Image" />
                        <div id="modalText">
                            <h3>Title</h3>
                            <p>Description</p>
                            <p>Updated at</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mouse Hover Tooltip -->
        <div id="previewTooltip">
            <img id="tooltipImage" src="" alt="Tooltip Preview" />
            <div id="tooltipInfo"></div>
        </div>

        <!-- Login Modal -->
        <div id="loginModal" class="auth-modal-overlay">
            <div class="auth-modal">
                <div class="auth-header">
                    <span>✨ Welcome Back to KempireS ✨</span>
                    <button class="auth-close-btn">&times;</button>
                </div>
                <div class="auth-body">
                    <form class="auth-form" id="loginForm">
                        <div class="form-group">
                            <label for="loginUsername">👤 Username or Email</label>
                            <input type="text" id="loginUsername" class="form-control" placeholder="Enter your username or email" required>
                        </div>

                        <div class="form-group">
                            <label for="loginPassword">🔒 Password</label>
                            <div class="password-wrapper">
                                <input type="password" id="loginPassword" class="form-control" placeholder="Enter your password" required>
                                <button type="button" class="toggle-password">👁️</button>
                            </div>
                        </div>

                        <div class="error-message" id="loginErrorMsg"></div>

                        <button type="submit" class="auth-btn">Login</button>

                        <div style="display: flex; justify-content: space-between; margin-top: 15px; font-size: 14px;">
                            <a href="#" id="forgotPasswordLink" style="color: #00a8cc; text-decoration: none;">Forgot Password?</a>
                            <a href="#" id="switchToRegister" style="color: #00a8cc; text-decoration: none;">Create New Account</a>
                        </div>
                    </form>

                    <div class="qr-section">
                        <div class="qr-title">Scan QR Code for Quick Login</div>
                        <div class="qr-code">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=https://example.com/loginQR" alt="Login QR Code">
                        </div>
                        <div class="qr-desc">Use KempireS mobile app to scan for quick login</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Register Modal -->
        <div id="registerModal" class="auth-modal-overlay">
            <div class="auth-modal">
                <div class="auth-header">
                    <span>✨ Welcome to KempireS ✨</span>
                    <button class="auth-close-btn">&times;</button>
                </div>
                <div class="auth-body">
                    <div class="register-progress">
                        <div class="progress-step active" data-step="1">1</div>
                        <div class="progress-step" data-step="2">2</div>
                        <div class="progress-step" data-step="3">3</div>
                    </div>

                    <form class="auth-form" id="registerForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">👤 First Name</label>
                                <input type="text" id="firstName" class="form-control" placeholder="Enter your first name" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">👤 Last Name</label>
                                <input type="text" id="lastName" class="form-control" placeholder="Enter your last name" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="birthdate">🎂 Birthday</label>
                            <input type="date" id="birthdate" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="gender">👫 Gender</label>
                            <select id="gender" class="form-control" required>
                                <option value="">Please select gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="language">🌐 Language</label>
                            <select id="language" class="form-control" required>
                                <option value="">Please select language</option>
                                <option value="zh_TW">繁體中文</option>
                                <option value="zh_CN">简体中文</option>
                                <option value="en">English</option>
                                <option value="ja">日本語</option>
                                <option value="ko">한국어</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="email">📧 Email</label>
                            <input type="email" id="email" class="form-control" placeholder="<EMAIL>" required>
                            <div class="error-message" id="emailErrorMsg"></div>
                        </div>

                        <div class="form-group">
                            <label for="password">🔒 Password</label>
                            <div class="password-wrapper">
                                <input type="password" id="password" class="form-control" placeholder="Set a secure password" required>
                                <button type="button" class="toggle-password">👁️</button>
                            </div>
                            <div class="password-strength-meter">
                                <div class="password-strength-value"></div>
                            </div>
                            <div class="password-requirements" id="passwordRequirements">
                                Password must be at least 10 characters, including uppercase, lowercase, numbers and symbols
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirmPassword">🔒 Confirm Password</label>
                            <div class="password-wrapper">
                                <input type="password" id="confirmPassword" class="form-control" placeholder="Enter password again" required>
                                <button type="button" class="toggle-password">👁️</button>
                            </div>
                            <div class="error-message" id="passwordMatchErrorMsg"></div>
                        </div>

                        <div class="error-message" id="registerErrorMsg"></div>

                        <button type="submit" class="auth-btn">Complete Registration</button>

                        <div style="text-align: center; margin-top: 15px; font-size: 14px; color: #666;">
                            Already have an account? <a href="#" id="switchToLogin" style="color: #00a8cc; text-decoration: none;">Click here to login</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Forgot Password Modal -->
        <div id="forgotPasswordModal" class="auth-modal-overlay">
            <div class="auth-modal">
                <div class="auth-header">
                    <span>🔑 Reset Your Password</span>
                    <button class="auth-close-btn">&times;</button>
                </div>
                <div class="auth-body">
                    <form class="auth-form" id="forgotPasswordForm">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=reset" alt="Reset Icon" style="width: 80px; height: 80px; border-radius: 50%; padding: 10px; background: rgba(52, 235, 213, 0.1);">
                            <p style="margin-top: 15px; color: #555; font-size: 15px;">Please enter your email address and we will send you a password reset link.</p>
                        </div>

                        <div class="form-group">
                            <label for="resetEmail">📧 Email</label>
                            <input type="email" id="resetEmail" class="form-control" placeholder="Enter your registered email" required>
                            <div class="error-message" id="resetEmailErrorMsg"></div>
                        </div>

                        <div class="error-message" id="forgotPasswordErrorMsg"></div>
                        <div id="forgotPasswordSuccessMsg" style="color: #27ae60; display: none; margin: 15px 0; padding: 10px; background-color: rgba(39, 174, 96, 0.1); border-radius: 8px; text-align: center;"></div>

                        <button type="submit" class="auth-btn">Send Password Reset Link</button>

                        <div style="text-align: center; margin-top: 15px; font-size: 14px; color: #666;">
                            <a href="#" id="backToLogin" style="color: #00a8cc; text-decoration: none;">Back to Login</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- User Guide Modal -->
        <div id="guideModal" class="guide-modal-overlay">
            <div class="guide-modal">
                <div class="guide-header">
                    <span>🏠 KMS Rental Grid User Guide</span>
                    <button class="guide-close-btn" id="guideCloseBtn">&times;</button>
                </div>
                <div class="guide-body">
                    <div class="guide-content">
                        <div class="guide-section">
                            <div class="guide-icon">🎯</div>
                            <h3>Welcome to KMS Rental Grid</h3>
                            <p>This is an innovative virtual real estate platform that allows you to own, rent, and manage properties in the digital world.</p>
                        </div>

                        <div class="guide-section">
                            <div class="guide-icon">🗺️</div>
                            <h3>Grid System</h3>
                            <p>• Select property levels (A-P) to browse different areas<br>
                               • Click left side number buttons (1-40) to switch to different plots<br>
                               • Each plot contains multiple rentable property units</p>
                        </div>

                        <div class="guide-section">
                            <div class="guide-icon">🏘️</div>
                            <h3>Real Estate Features</h3>
                            <p>• <strong>Rent/Buy/Sell Land:</strong> Manage your real estate investment portfolio<br>
                               • <strong>Room Management:</strong> Rent, auction, add rooms<br>
                               • <strong>House Upgrade:</strong> Expand your property scale</p>
                        </div>

                        <div class="guide-section">
                            <div class="guide-icon">👑</div>
                            <h3>VIP Exclusive Features</h3>
                            <p>• <strong>Emperor Exclusive:</strong> Highest level property areas<br>
                               • <strong>VIP Properties:</strong> Enjoy exclusive privileges and premium services<br>
                               • <strong>KMS App Store:</strong> Purchase exclusive apps and services</p>
                        </div>

                        <div class="guide-section">
                            <div class="guide-icon">🔍</div>
                            <h3>Search & Social</h3>
                            <p>• Use the top search bar to find users, properties or hot topics<br>
                               • Browse beauty rankings, hot topics, entertainment news, etc.<br>
                               • Communicate with other users in real-time through Live Chat</p>
                        </div>

                        <div class="guide-section">
                            <div class="guide-icon">📞</div>
                            <h3>Customer Support</h3>
                            <p>• 24/7 customer service center to assist you<br>
                               • View terms of service and privacy policy<br>
                               • Provide user feedback to help us improve</p>
                        </div>

                        <div class="guide-actions">
                            <button class="guide-btn guide-btn-primary" id="guideLoginBtn">
                                <span class="btn-icon">🚀</span>
                                Login to Start Experience
                            </button>
                            <button class="guide-btn guide-btn-secondary" id="guideRegisterBtn">
                                <span class="btn-icon">✨</span>
                                Register New Account
                            </button>
                            <button class="guide-btn guide-btn-explore" id="guideExploreBtn">
                                <span class="btn-icon">👀</span>
                                Explore as Guest
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Chat 對話視窗 -->
        <div id="liveChatWindow" class="live-chat-window">
            <div class="iphone-container">
                <!-- iPhone 頂部狀態欄 -->
                <div class="iphone-status-bar">
                    <div class="status-left">
                        <span class="time">14:30</span>
                    </div>
                    <div class="status-center">
                        <div class="notch"></div>
                    </div>
                    <div class="status-right">
                        <span class="battery">89%</span>
                        <span class="battery-icon">🔋</span>
                        <span class="wifi-icon">📶</span>
                    </div>
                </div>

                <!-- 聊天頭部 -->
                <div class="chat-header">
                    <button class="chat-back-btn" id="chatCloseBtn">
                        <span class="back-arrow">←</span>
                    </button>
                    <div class="chat-profile">
                        <div class="chat-avatar">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=50x50&data=KMS&bgcolor=34ebd5" alt="客服頭像">
                            <span class="online-indicator"></span>
                        </div>
                        <div class="chat-info">
                            <div class="chat-name">KMS Customer Service Center</div>
                            <div class="chat-status">Online • Usually replies within minutes</div>
                        </div>
                    </div>
                    <button class="chat-options-btn">⋮</button>
                </div>

                <!-- Chat Content Area -->
                <div class="chat-messages" id="chatMessages">
                    <div class="chat-day-divider">
                        <span>Today</span>
                    </div>

                    <div class="message-system">
                        <span>You are connected to KMS Customer Service Center</span>
                    </div>

                    <div class="message-received">
                        <div class="message-content">
                            <p>Hello! Welcome to KMS Customer Service Center, how may I help you?</p>
                        </div>
                        <div class="message-time">14:25</div>
                    </div>

                    <!-- Messages will be dynamically added here -->
                </div>

                <!-- Waiting Indicator -->
                <div class="waiting-indicator" id="waitingIndicator" style="display: none;">
                    <div class="waiting-time">
                        <div class="waiting-icon">⏱️</div>
                        <div class="waiting-text">Estimated waiting time: <span id="estimatedWaitTime">2</span> minutes</div>
                    </div>
                    <div class="queue-position">
                        Your current position in queue: <span id="queuePosition">3</span>
                    </div>
                </div>

                <!-- 聊天輸入區域 -->
                <div class="chat-input-area">
                    <div class="chat-input-container">
                        <button class="chat-attach-btn">+</button>
                        <input type="text" id="chatInput" class="chat-input" placeholder="輸入訊息...">
                        <button class="chat-emoji-btn">😊</button>
                    </div>
                    <button class="chat-send-btn" id="chatSendBtn">
                        <span class="send-icon">↑</span>
                    </button>
                </div>

                <!-- iPhone 底部指示條 -->
                <div class="iphone-home-indicator"></div>
            </div>
        </div>

        <!-- Process shim for Node.js globals -->
        <script>
            window.process = { env: { NODE_ENV: 'development' } };
        </script>
        

        
        <!-- Main JavaScript File -->
        <script type="module" src="JS/main.js"></script>
    </body>
</html>
