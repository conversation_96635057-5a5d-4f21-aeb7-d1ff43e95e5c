/* ===========
   Basic Styles
   =========== */
body {
    width: 100vw;
    height: 100vh;
    background-image: url("/BG/KMS_Nature/KMS_Nature_001.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    background-attachment: fixed;
    overflow: hidden;
    margin: 0; /* 去除預設 margin */
}
.grid-container {
    display: grid;
    grid-template-columns: repeat(20, 1fr);
    grid-template-rows: repeat(12, 1fr);
    gap: 2px;
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    padding: 15px;
}
.outer-grid-item {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    user-select: none;
    background-color: #0c2491;
    border-radius: 14px;
    text-align: center;
    overflow: hidden;
}
.inner-grid {
    grid-column: 2 / span 18;
    grid-row: 2 / span 10;
    display: grid;
    gap: 1px;
}
.grid-item {
    background-color: #292626;
    border: 1px solid #ffffff;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    user-select: none;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.grid-item:hover {
    outline: 5px solid rgba(0, 255, 255, 0.8);
    z-index: 900;
}
.grid-item.selected {
    background-color: #34ebd5;
    color: #333;
}
.toggle-button {
    width: 100%;
    height: 100%;
    background-color: #1462d1;
    color: #fff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.toggle-button-label {
    font-size: 16px;
    padding: 4px 0;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    width: 100%;
    z-index: 2;
}

.toggle-button:hover {
    background-color: #ffd700;
}

/* 選擇中的按鈕樣式 */
.toggle-button.active {
    background-color: #ffd700;
    color: #fffb00;
}

/* Grid Size Display Styles */
.grid-size {
    font-size: 14px;
    display: inline-block;
    margin-top: 10px;
}

/* 3D 標題樣式 */
.title-3d {
    position: absolute;
    top: -20%;
    font-size: 38px;
    color: rgb(0, 255, 225);
    text-align: center;
    padding: 10px 0;
    letter-spacing: 3px;
    font-family: Arial, sans-serif;
    text-shadow:
        0 1px 0 #2d2c2c,
        0 2px 0 #5b5a5a,
        0 3px 0 #595858,
        0 4px 0 #302f2f,
        0 5px 0 #1a1a1a,
        0 6px 1px rgba(0,0,0,.1),
        0 0 5px rgba(0,0,0,.1),
        0 1px 3px rgba(0,0,0,.3),
        0 3px 5px rgba(0,0,0,.2),
        0 5px 10px rgba(0,0,0,.25),
        0 10px 10px rgba(0,0,0,.2),
        0 20px 20px rgba(0,0,0,.15);
    transform: perspective(500px) rotateX(10deg);
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* ===========
   Modal Styles
   =========== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
}
.win-modal {
    width: 90vw;
    height: 90vh;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 14px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    backdrop-filter: blur(20px);
}
.titlebar {
    padding: 12px;
    background: rgba(0, 177, 212, 0.8);
    color: white;
    display: flex;
    justify-content: center; /* 標題置中 */
    align-items: center;
    position: relative; /* 為關閉按鈕定位 */
}
.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 0 12px;
    border-radius: 12px;
    position: absolute;
    right: 10px;
}
.modal-content {
    padding: 20px;
    height: calc(100% - 60px);
    overflow: auto;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.modal-image {
    width: 50%;
    height: auto;
    max-height: 40vh;
    object-fit: cover;
    border-radius: 14px;
    margin: 0 auto 20px;
    display: block;
}

/* 預載(loading) 時的動畫 */
.modal-image.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}
@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
.loading-text {
    color: #666;
    font-style: italic;
}

#modalText {
    text-align: center;
    width: 80%;
    margin: 0 auto;
}
#modalText h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: #fff;
}
#modalText p {
    font-size: 16px;
    line-height: 1.6;
    color: #fff;
    margin-bottom: 10px;
}

/* ===========
   Tooltip
   =========== */
#previewTooltip {
    position: absolute;
    width: 400px;
    height: 300px;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    display: none; /* 預設不顯示 */
    z-index: 2000; /* 在網格項目上層 */
    pointer-events: none; /* 滑鼠穿透，避免干擾 */
    padding: 8px;
}
#previewTooltip img {
    width: 100%;
    height: 70%;
    object-fit: cover;
    border-radius: 4px;
}
#tooltipInfo {
    margin-top: 6px;
    font-size: 14px;
    text-align: center;
}

/* =============
   40 種模式顏色
   ============= */
.content-mode-1 .grid-item  { background: #ffb3ba; }
.content-mode-2 .grid-item  { background: #ffdfba; }
.content-mode-3 .grid-item  { background: #ffffba; }
.content-mode-4 .grid-item  { background: #baffc9; }
.content-mode-5 .grid-item  { background: #bae1ff; }
.content-mode-6 .grid-item  { background: #e0baff; }
.content-mode-7 .grid-item  { background: #ffbaf0; }
.content-mode-8 .grid-item  { background: #bab3ff; }
.content-mode-9 .grid-item  { background: #ffc3a0; }
.content-mode-10 .grid-item { background: #a0ffe0; }
.content-mode-11 .grid-item { background: #d4a5a5; }
.content-mode-12 .grid-item { background: #a5d4ae; }
.content-mode-13 .grid-item { background: #a5aed4; }
.content-mode-14 .grid-item { background: #d4a5d1; }
.content-mode-15 .grid-item { background: #d4d1a5; }
.content-mode-16 .grid-item { background: #a5d4d1; }
.content-mode-17 .grid-item { background: #d1d4a5; }
.content-mode-18 .grid-item { background: #a5a5d4; }
.content-mode-19 .grid-item { background: #ffa07a; }
.content-mode-20 .grid-item { background: #7affaa; }
.content-mode-21 .grid-item { background: #7aafff; }
.content-mode-22 .grid-item { background: #ff7a7a; }
.content-mode-23 .grid-item { background: #7aff7a; }
.content-mode-24 .grid-item { background: #7a7aff; }
.content-mode-25 .grid-item { background: #ffd700; }
.content-mode-26 .grid-item { background: #d7ff00; }
.content-mode-27 .grid-item { background: #00ffd7; }
.content-mode-28 .grid-item { background: #d700ff; }
.content-mode-29 .grid-item { background: #00d7ff; }
.content-mode-30 .grid-item { background: #ff00d7; }
.content-mode-31 .grid-item { background: #8a2be2; }
.content-mode-32 .grid-item { background: #2be28a; }
.content-mode-33 .grid-item { background: #e22b2b; }
.content-mode-34 .grid-item { background: #2b2be2; }
.content-mode-35 .grid-item { background: #e2e22b; }
.content-mode-36 .grid-item { background: #2be2e2; }
.content-mode-37 .grid-item { background: #e2b72b; }
.content-mode-38 .grid-item { background: #b72be2; }
.content-mode-39 .grid-item { background: #2be2b7; }
.content-mode-40 .grid-item { background: #b7e22b; }

/* 方形按鈕容器 */
.square-buttons-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 3px;
    width: 100%;
    flex: 1;
    padding: 5px;
    box-sizing: border-box;
}

/* 16格按鈕容器 */
.square-buttons-container-16 {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr 1fr;
    gap: 2px;
    width: 100%;
    flex: 1;
    padding: 4px;
    box-sizing: border-box;
    z-index: 5;
}

/* 熱門話題樣式 */
.topic-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #444, #222);
}

.topic-button:hover {
    background: linear-gradient(135deg, #333, #111);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.topic-icon {
    font-size: 24px;
    margin: 5px 0;
}

/* 當前在線人數樣式 */
.online-status-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
}

.online-status-button:hover {
    background: linear-gradient(135deg, #1e3c72, #354d88);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.online-status-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 5px;
    width: 100%;
}

.location-text {
    font-size: 14px;
    margin-bottom: 5px;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.online-count {
    font-size: 14px;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.count-number {
    font-weight: bold;
    color: #34ebd5;
    font-size: 16px;
    margin-top: 10px;
}

/* 高級房地產按鈕樣式 */
.premium-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #3a1c71, #d76d77, #ffaf7b);
    overflow: hidden;
    position: relative;
}

.premium-button::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 45%,
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0.1) 55%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

.premium-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.premium-icon {
    font-size: 24px;
    margin: 5px 0;
    z-index: 2;
}

/* 凱帝王專屬樣式 */
.premium-button[data-premium="emperor"] {
    background: linear-gradient(135deg, #b78628, #fcc201, #b78628);
}

.premium-button[data-premium="emperor"] .premium-icon {
    font-size: 32px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* App Store 按鈕樣式 */
.app-store-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #2193b0, #6dd5ed);
}

.app-store-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #1c839f, #5bc2db);
}

.app-store-icon {
    font-size: 28px;
    margin: 5px 0;
}

/* Logo 容器調整 */
.logo-container {
    z-index: 11;
    overflow: visible;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* 搜尋列樣式 */
.search-container {
    z-index: 11;
    width: 90%;
    height: 36px;
    padding: 0;
    margin-top: 4px;
    position: absolute;
    bottom: 8px;
}

.search-bar {
    display: flex;
    width: 100%;
    height: 100%;
    background: rgba(30, 33, 40, 0.95);
    border-radius: 20px;
    overflow: hidden;
    border: 2px solid rgba(52, 235, 213, 0.9);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.search-bar:hover, .search-bar:focus-within {
    background: rgba(20, 23, 30, 0.98);
    border-color: rgba(52, 235, 213, 1);
    box-shadow: 0 4px 15px rgba(52, 235, 213, 0.4);
}

.search-bar input {
    flex: 1;
    height: 100%;
    padding: 0 15px;
    border: none;
    background: transparent;
    color: white;
    font-size: 14px;
    outline: none;
    font-weight: 500;
}

.search-bar input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-bar button {
    width: 36px;
    height: 100%;
    border: none;
    background: rgba(52, 235, 213, 1);
    color: #111;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background 0.2s;
}

.search-bar button:hover {
    background: rgba(82, 255, 233, 1);
}

.search-icon {
    font-size: 18px;
}

/* 方形按鈕 */
.square-button {
    background-color: #061869;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    color: white;
    user-select: none;
    transition: all 0.2s ease;
}

.square-button:hover {
    background-color: #34ebd5;
    color: #08aba5;
    transform: scale(1.05);
}

/* 升級房子按鈕 */
.house-upgrade-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.house-upgrade-btn:hover {
    background-color: rgba(52, 235, 213, 0.2);
}

.upgrade-icon {
    font-size: 28px;
    margin-bottom: 5px;
}

.upgrade-text {
    font-size: 16px;
    font-weight: bold;
    color: #34ebd5;
}

/* 個人資料編輯按鈕 */
.profile-edit-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.profile-edit-btn:hover {
    background-color: rgba(52, 235, 213, 0.2);
}

.profile-icon {
    font-size: 32px;
    margin-bottom: 5px;
}

.profile-text {
    font-size: 16px;
    font-weight: bold;
    color: #34ebd5;
}

/* 房子架構編輯按鈕 */
.house-struct-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.house-struct-btn:hover {
    background-color: rgba(52, 235, 213, 0.2);
}

.struct-icon {
    font-size: 32px;
    margin-bottom: 5px;
}

.struct-text {
    font-size: 16px;
    font-weight: bold;
    color: #34ebd5;
}

/* 小型方形按鈕 (用於16格網格) */
.square-button-small {
    position: relative !important;
    z-index: 9999 !important; /* 最高堆疊順序 */
    background-color: #00bcff;
    border: none;
    border-radius: 3px;
    cursor: pointer !important; /* 確保滑鼠事件 */
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    color: white;
    user-select: none;
    transition: background-color 0.2s ease;
    pointer-events: auto !important; /* 確保滑鼠事件 */
}

.square-button-small:hover {
    background-color: #fffb00;
}

/* 小按鈕選中狀態樣式 */
.square-button-small.active {
    background-color: #00ffe1 !important;
    color: #333 !important;
    box-shadow: 0 0 4px rgba(52, 235, 213, 0.6) !important;
}

/* u7db2u683cu5c3au5bf8u986fu793au6a23u5f0f */

/* ===========
   User Guide Modal
   =========== */
.guide-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(15px);
}

.guide-modal {
    width: 100vw;
    max-width: 1600px;
    max-height: 130vh; /* Increased by 40vh total */
    background: linear-gradient(135deg, rgba(20, 98, 209, 0.95), rgba(52, 235, 213, 0.95));
    border-radius: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    backdrop-filter: blur(20px);
    animation: guideModalSlideIn 0.5s ease-out;
    display: flex;
    flex-direction: column;
}

@keyframes guideModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes guideModalSlideOut {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
}

.guide-header {
    padding: 20px;
    background: linear-gradient(135deg, rgba(12, 36, 145, 0.9), rgba(20, 98, 209, 0.9));
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.guide-close-btn {
    position: absolute;
    right: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.guide-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.guide-body {
    padding: 30px 30px 100px; /* Add bottom padding for buttons */
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto; /* Allow scrolling if needed */
}

.guide-content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
    gap: 1.5rem;
    flex: 1;
    overflow: auto;
}

.guide-section {
    background: rgba(255, 255, 255, 0.15);
    padding: 20px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.guide-section:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.guide-icon {
    font-size: 48px;
    text-align: center;
    margin-bottom: 15px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.guide-section h3 {
    color: white;
    font-size: 44px;
    margin-bottom: 12px;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.guide-section p {
    color: rgba(255, 255, 255, 0.95);
    font-size: 32px;
    line-height: 1.6;
    text-align: center;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.guide-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 20px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
    position: sticky;
    bottom: 10px; /* Position above the bottom padding */
    z-index: 10;
    margin-top: auto; /* Push to bottom */
}

.guide-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px 25px;
    border: none;
    border-radius: 12px;
    font-size: 36px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    flex: 1;
    max-width: 30%;
}

.guide-btn-primary {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.guide-btn-primary:hover {
    background: linear-gradient(135deg, #ffed4e, #ffd700);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6);
}

.guide-btn-secondary {
    background: linear-gradient(135deg, #34ebd5, #00a8cc);
    color: white;
    box-shadow: 0 4px 15px rgba(52, 235, 213, 0.4);
}

.guide-btn-secondary:hover {
    background: linear-gradient(135deg, #00a8cc, #34ebd5);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 235, 213, 0.6);
}

.guide-btn-explore {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.guide-btn-explore:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
}

/* 幫助按鈕樣式 */
.help-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 215, 0, 0.9);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.help-button:hover {
    background: rgba(255, 215, 0, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.help-icon {
    font-size: 18px;
    color: #333;
}

.logo-container .toggle-button {
    position: relative;
}
.btn-icon {
    font-size: 20px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .guide-modal {
        width: 100vw;
        max-width: none;
        margin: 10px;
    }
    
    .guide-header {
        padding: 15px;
        font-size: 20px;
    }
    
    .guide-body {
        padding: 20px;
    }
    
    .guide-section {
        padding: 15px;
    }
    
    .guide-icon {
        font-size: 36px;
    }
    
    .guide-section h3 {
        font-size: 18px;
    }
    
    .guide-section p {
        font-size: 14px;
    }
    
    .guide-btn {
        padding: 12px 20px;
        font-size: 16px;
    }
}
/* ===========
   Login Registration Modal Styles
   =========== */
.auth-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.auth-modal {
    width: 620px;
    max-width: 90%;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.15);
    overflow: hidden;
    position: relative;
    animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    transform: translateY(0);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header {
    background: linear-gradient(135deg, #34ebd5, #00a8cc);
    color: white;
    padding: 25px 20px;
    text-align: center;
    font-size: 26px;
    position: relative;
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.auth-close-btn {
    position: absolute;
    right: 15px;
    top: 15px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 22px;
    cursor: pointer;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.auth-close-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: rotate(90deg);
}

.auth-body {
    padding: 30px;
    max-height: 70vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #34ebd5 #f0f0f0;
}

.auth-body::-webkit-scrollbar {
    width: 8px;
}

.auth-body::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 10px;
}

.auth-body::-webkit-scrollbar-thumb {
    background: #34ebd5;
    border-radius: 10px;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
    position: relative;
    transition: all 0.3s;
}

.form-group label {
    font-weight: 500;
    font-size: 14px;
    color: #333;
    transition: color 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.form-group:focus-within label {
    color: #34ebd5;
}

.form-control {
    height: 46px;
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s;
    background-color: rgba(255, 255, 255, 0.9);
}

.form-control:hover {
    border-color: #ccc;
}

.form-control:focus {
    border-color: #34ebd5;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 235, 213, 0.25);
    background-color: #fff;
}

.auth-form .error-message {
    color: #e74c3c;
    font-size: 14px;
    margin-top: 5px;
    display: none;
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
    40%, 60% { transform: translate3d(3px, 0, 0); }
}

.auth-form .error-message.visible {
    display: block;
}

.auth-btn {
    height: 50px;
    background: linear-gradient(135deg, #34ebd5, #00a8cc);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 10px;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(52, 235, 213, 0.3);
}

.auth-btn:hover {
    background: linear-gradient(135deg, #2ad1bd, #0095b5);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 235, 213, 0.4);
}

.auth-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 10px rgba(52, 235, 213, 0.3);
}

.auth-btn::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 45%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0.1) 55%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(45deg);
    animation: btnShimmer 3s infinite;
    z-index: 1;
}

@keyframes btnShimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

.auth-footer {
    padding: 20px 25px;
    text-align: center;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(0, 0, 0, 0.02);
}

.auth-footer a {
    color: #00a8cc;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s;
}

.auth-footer a:hover {
    color: #34ebd5;
    text-decoration: underline;
}

.form-row {
    display: flex;
    gap: 18px;
}

.form-row .form-group {
    flex: 1;
}

.qr-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 25px;
    padding-top: 25px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.qr-code {
    width: 160px;
    height: 160px;
    background-color: #fff;
    padding: 12px;
    border-radius: 16px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    transition: all 0.3s;
    border: 2px solid rgba(52, 235, 213, 0.3);
}

.qr-code:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.qr-code img {
    width: 100%;
    height: 100%;
}

.qr-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
}

.qr-desc {
    font-size: 14px;
    color: #666;
    text-align: center;
}

.password-wrapper {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: none;
    color: #777;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 18px;
    padding: 5px;
}

.toggle-password:hover {
    color: #34ebd5;
}

.password-requirements {
    font-size: 13px;
    color: #666;
    margin-top: 8px;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    border-left: 3px solid #ccc;
    transition: all 0.3s;
}

.password-requirements.valid {
    color: #27ae60;
    background-color: rgba(39, 174, 96, 0.05);
    border-left-color: #27ae60;
}

.password-requirements.invalid {
    color: #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
    border-left-color: #e74c3c;
}

/* 註冊表單進度指示器 */
.register-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    position: relative;
}

.register-progress::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e0e0e0;
    transform: translateY(-50%);
    z-index: 1;
}

.progress-step {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #fff;
    position: relative;
    z-index: 2;
}

.progress-step.active {
    background-color: #34ebd5;
}

.progress-step.completed {
    background-color: #27ae60;
}

/* 表單輸入動畫效果 */
.form-control {
    transform-origin: left;
}

.form-group:focus-within .form-control {
    animation: pulseInput 0.3s ease-out;
}

@keyframes pulseInput {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* 密碼強度指示器 */
.password-strength-meter {
    height: 5px;
    background-color: #e0e0e0;
    border-radius: 3px;
    margin-top: 8px;
    overflow: hidden;
    position: relative;
}

.password-strength-value {
    height: 100%;
    width: 0;
    border-radius: 3px;
    transition: width 0.3s, background-color 0.3s;
}

.strength-weak .password-strength-value {
    width: 25%;
    background-color: #e74c3c;
}

.strength-medium .password-strength-value {
    width: 50%;
    background-color: #f39c12;
}

.strength-strong .password-strength-value {
    width: 75%;
    background-color: #3498db;
}

.strength-very-strong .password-strength-value {
    width: 100%;
    background-color: #27ae60;
}

.login-register-btns {
    display: inline-grid;
    gap: 4px;
    width: -webkit-fill-available;
    height: -webkit-fill-available;
    margin: 6px;
}

.login-btn, .register-btn {
    flex: 1;
    padding: 8px 5px;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
}

.login-btn {
    background-color: #0078d4;
    color: white;
}

.register-btn {
    background-color: #34ebd5;
    color: #333;
}

.login-btn:hover, .register-btn:hover {
    filter: brightness(1.1);
}

/* 服務按鈕 */
.service-button {
    display: flex;
    flex: 1;
    width: 100%;
    height: auto;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 5px;
    transition: all 0.3s ease;
}

.service-button:hover {
    background-color: #2a2a2a;
}

.service-icon {
    font-size: 22px;
    margin-top: 5px;
}

/* 服務內容樣式 */
.service-content {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    color: #fff;
    text-align: left;
}

.service-content h3 {
    font-size: 24px;
    margin-bottom: 20px;
    text-align: center;
}

.service-content h4 {
    font-size: 18px;
    margin-top: 25px;
    margin-bottom: 10px;
    color: #34ebd5;
}

.service-section, .terms-section {
    margin-bottom: 25px;
}

/* ===========
   Live Chat 按鈕樣式
   =========== */
.live-chat-button {
    background: linear-gradient(135deg, #00c6ff, #0072ff);
    transition: all 0.3s ease;
}

.live-chat-button:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #00d2ff, #0080ff);
    box-shadow: 0 8px 16px rgba(0, 114, 255, 0.3);
}

.live-chat-icon-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
}

.live-chat-icon {
    font-size: 32px;
    margin-bottom: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.live-chat-status {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

/* ===========
   Live Chat 視窗樣式 (iPhone 風格 - Dark Theme)
   =========== */
.live-chat-window {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 525px;   /* Increased by 50% */
    height: 900px;  /* Increased by 50% */
    z-index: 3000;
    display: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    border-radius: 40px;
    overflow: hidden;
}

.iphone-container {
    width: 100%;
    height: 100%;
    background-color: #121212; /* Dark background */
    display: flex;
    flex-direction: column;
    position: relative;
    color: #e0e0e0; /* Light text color */
}

/* iPhone 頂部狀態欄 */
.iphone-status-bar {
    height: 66px; /* 44px * 1.5 */
    background-color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 22px; /* 15px * 1.5 */
    color: white;
    font-size: 21px; /* 14px * 1.5 */
}

.status-center {
    position: relative;
    height: 45px; /* 30px * 1.5 */
    width: 225px; /* 150px * 1.5 */
}

.notch {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 180px; /* 120px * 1.5 */
    height: 45px; /* 30px * 1.5 */
    background-color: #000;
    border-bottom-left-radius: 27px; /* 18px * 1.5 */
    border-bottom-right-radius: 27px; /* 18px * 1.5 */
}

.status-right {
    display: flex;
    align-items: center;
    gap: 7px; /* 5px * 1.5 */
}

/* 聊天頭部 */
.chat-header {
    height: 90px; /* 60px * 1.5 */
    background-color: #1a1a1a; /* Dark header */
    display: flex;
    align-items: center;
    padding: 0 22px; /* 15px * 1.5 */
    border-bottom: 1px solid #333;
}

.chat-back-btn {
    background: none;
    border: none;
    font-size: 30px; /* 20px * 1.5 */
    color: #34ebd5; /* Accent color */
    cursor: pointer;
    padding: 7px; /* 5px * 1.5 */
    margin-right: 15px; /* 10px * 1.5 */
}

.chat-profile {
    display: flex;
    align-items: center;
    flex: 1;
}

.chat-avatar {
    width: 60px; /* 40px * 1.5 */
    height: 60px; /* 40px * 1.5 */
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px; /* 10px * 1.5 */
    position: relative;
    border: 2px solid #34ebd5;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.online-indicator {
    position: absolute;
    bottom: 3px; /* 2px * 1.5 */
    right: 3px; /* 2px * 1.5 */
    width: 15px; /* 10px * 1.5 */
    height: 15px; /* 10px * 1.5 */
    background-color: #4cd964;
    border-radius: 50%;
    border: 3px solid #121212; /* 2px * 1.5 */
}

.chat-info {
    display: flex;
    flex-direction: column;
}

.chat-name {
    font-weight: 600;
    font-size: 24px; /* 16px * 1.5 */
    color: #ffffff;
}

.chat-status {
    font-size: 18px; /* 12px * 1.5 */
    color: #a0a0a0;
}

.chat-options-btn {
    background: none;
    border: none;
    font-size: 30px; /* 20px * 1.5 */
    color: #a0a0a0;
    cursor: pointer;
    padding: 7px; /* 5px * 1.5 */
}

/* 聊天內容區域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 22px; /* 15px * 1.5 */
    background-color: #0a0a0a; /* Dark background */
    display: flex;
    flex-direction: column;
    gap: 22px; /* 15px * 1.5 */
}

.chat-day-divider {
    text-align: center;
    margin: 15px 0; /* 10px * 1.5 */
    position: relative;
}

.chat-day-divider span {
    background-color: rgba(52, 235, 213, 0.2);
    color: #34ebd5;
    font-size: 18px; /* 12px * 1.5 */
    padding: 4px 15px; /* 3px*1.5, 10px*1.5 */
    border-radius: 15px; /* 10px * 1.5 */
}

.message-system {
    text-align: center;
    margin: 15px 0; /* 10px * 1.5 */
}

.message-system span {
    background-color: rgba(52, 235, 213, 0.2);
    color: #34ebd5;
    font-size: 18px; /* 12px * 1.5 */
    padding: 7px 15px; /* 5px*1.5, 10px*1.5 */
    border-radius: 15px; /* 10px * 1.5 */
}

.message-received {
    align-self: flex-start;
    max-width: 80%;
    display: flex;
    flex-direction: column;
}

.message-sent {
    align-self: flex-end;
    max-width: 80%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.message-content {
    padding: 15px 22px; /* 10px*1.5, 15px*1.5 */
    border-radius: 27px; /* 18px * 1.5 */
    font-size: 24px; /* 16px * 1.5 */
    line-height: 1.4;
}

.message-received .message-content {
    background-color: #2a2a2a;
    color: #e0e0e0;
    border-bottom-left-radius: 7px; /* 5px * 1.5 */
}

.message-sent .message-content {
    background-color: #007aff;
    color: #fff;
    border-bottom-right-radius: 7px; /* 5px * 1.5 */
}

.message-time {
    font-size: 16px; /* 11px * 1.5 */
    color: #a0a0a0;
    margin-top: 3px; /* 2px * 1.5 */
    margin-left: 7px; /* 5px * 1.5 */
}

.message-sent .message-time {
    margin-right: 7px; /* 5px * 1.5 */
}

/* 等待提示 */
.waiting-indicator {
    background-color: #1a1a1a;
    padding: 22px; /* 15px * 1.5 */
    border-top: 1px solid #333;
    border-bottom: 1px solid #333;
}

.waiting-time {
    display: flex;
    align-items: center;
    margin-bottom: 12px; /* 8px * 1.5 */
}

.waiting-icon {
    font-size: 30px; /* 20px * 1.5 */
    margin-right: 15px; /* 10px * 1.5 */
}

.waiting-text {
    font-size: 21px; /* 14px * 1.5 */
    color: #e0e0e0;
}

.queue-position {
    font-size: 19px; /* 13px * 1.5 */
    color: #a0a0a0;
}

/* 聊天輸入區域 */
.chat-input-area {
    height: 90px; /* 60px * 1.5 */
    background-color: #1a1a1a;
    display: flex;
    align-items: center;
    padding: 0 22px; /* 15px * 1.5 */
    border-top: 1px solid #333;
}

.chat-input-container {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #2a2a2a;
    border-radius: 30px; /* 20px * 1.5 */
    padding: 0 15px; /* 10px * 1.5 */
    margin-right: 15px; /* 10px * 1.5 */
    border: 1px solid #444;
}

.chat-attach-btn, .chat-emoji-btn {
    background: none;
    border: none;
    font-size: 30px; /* 20px * 1.5 */
    color: #a0a0a0;
    cursor: pointer;
    padding: 7px; /* 5px * 1.5 */
}

.chat-input {
    flex: 1;
    height: 54px; /* 36px * 1.5 */
    border: none;
    outline: none;
    font-size: 24px; /* 16px * 1.5 */
    padding: 0 15px; /* 10px * 1.5 */
    background-color: transparent;
    color: #e0e0e0;
}

.chat-send-btn {
    width: 54px; /* 36px * 1.5 */
    height: 54px; /* 36px * 1.5 */
    background-color: #007aff;
    border: none;
    border-radius: 50%;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.send-icon {
    font-size: 27px; /* 18px * 1.5 */
}

/* iPhone 底部指示條 */
.iphone-home-indicator {
    height: 7px; /* 5px * 1.5 */
    width: 60%; /* 40% * 1.5 */
    background-color: #444;
    border-radius: 4px; /* 3px * 1.5 */
    margin: 7px auto 12px; /* 5px*1.5, 8px*1.5 */
}

.service-section ul {
    list-style-type: disc;
    padding-left: 20px;
    margin: 10px 0;
}

.service-section ul li {
    margin-bottom: 5px;
}

.service-section a {
    color: #34ebd5;
    text-decoration: none;
}

.service-section a:hover {
    text-decoration: underline;
}

.service-chat-btn {
    background-color: #34ebd5;
    color: #333;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    margin-top: 10px;
}

.service-chat-btn:hover {
    background-color: #28b8a9;
}

.contact-info {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.contact-info p {
    margin: 5px 0;
}

/* 反饋表單樣式 */
.feedback-form {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.feedback-form .form-group {
    margin-bottom: 20px;
}

.feedback-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.feedback-form .form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.2);
    color: #fff;
}

.feedback-form textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

.rating-container {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-container input {
    display: none;
}

.rating-container label {
    display: inline-block;
    cursor: pointer;
    width: 30px;
    font-size: 30px;
    color: #ccc;
    margin: 0;
    transition: color 0.2s;
}

.rating-container input:checked ~ label,
.rating-container label:hover,
.rating-container label:hover ~ label {
    color: #ffcc00;
}

.success-message {
    background-color: rgba(39, 174, 96, 0.2);
    color: #27ae60;
    padding: 15px;
    border-radius: 5px;
    text-align: center;
    margin-top: 20px;
    font-weight: bold;
}

/* 高級房地產彈窗樣式 */
.premium-content {
    width: 100%;
    color: #fff;
    text-align: left;
}

.premium-description {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 20px;
    color: #e0e0e0;
}

.premium-features {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.premium-features h4 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #34ebd5;
}

.premium-features ul {
    list-style-type: none;
    padding: 0;
}

.premium-features li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
    font-size: 16px;
}

.premium-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #34ebd5;
    font-weight: bold;
}

.premium-pricing {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px 20px;
    text-align: center;
}

.premium-price {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
}

.premium-btn {
    background: linear-gradient(to right, #34ebd5, #00bfff);
    color: #fff;
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: bold;
    margin-top: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 235, 213, 0.5);
}

/* App Store 彈窗樣式 */
.app-store-content {
    width: 100%;
    color: #fff;
    text-align: left;
}

.app-store-description {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 20px;
    color: #e0e0e0;
}

.app-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.app-category {
    flex: 1;
    min-width: 250px;
}

.app-category h4 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #34ebd5;
    background: rgba(0, 0, 0, 0.3);
    padding: 8px 15px;
    border-radius: 5px;
}

.app-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.app-item {
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 10px;
    transition: all 0.3s ease;
}

.app-item:hover {
    background: rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.app-icon {
    font-size: 32px;
    margin-right: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.app-info {
    flex: 1;
}

.app-name {
    font-weight: bold;
    margin-bottom: 3px;
}

.app-rating {
    font-size: 12px;
    color: #ffd700;
}

.app-download-btn {
    background: #34ebd5;
    color: #111;
    border: none;
    border-radius: 15px;
    padding: 5px 15px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.app-download-btn:hover {
    background: #2bc4b0;
    transform: scale(1.05);
}

.app-store-more-btn {
    background: linear-gradient(to right, #2193b0, #6dd5ed);
    color: #fff;
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: bold;
    margin-top: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
    width: 200px;
    margin: 20px auto 0;
    text-align: center;
}

.app-store-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(33, 147, 176, 0.5);
}

/* 3D 按鈕樣式 */
.toggle-button-3d {
    background: linear-gradient(145deg, #2196f3, #00bcd4);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1),
               0 1px 3px rgba(0, 0, 0, 0.2),
               inset 0 -2px 0 rgba(0, 0, 0, 0.2),
               inset 0 2px 0 rgba(255, 255, 255, 0.2);
    transform-style: preserve-3d;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.toggle-button-3d:hover {
    background: linear-gradient(145deg, #ff9800, #f44336);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15),
               0 3px 6px rgba(0, 0, 0, 0.2),
               inset 0 -2px 0 rgba(0, 0, 0, 0.2),
               inset 0 2px 0 rgba(255, 255, 255, 0.2);
}

.toggle-button-3d:active {
    transform: translateY(1px);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1),
               0 1px 2px rgba(0, 0, 0, 0.1),
               inset 0 -1px 0 rgba(0, 0, 0, 0.2),
               inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.toggle-button-3d .toggle-button-label {
    background-color: rgba(0, 0, 0, 0.3);
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.toggle-button-3d .topic-icon {
    font-size: 24px;
    margin-top: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
/* Marquee styles */
.toggle-button.marquee {
    overflow: hidden;
    background-color: #1462d1;
    display: flex;
    align-items: center;
    justify-content: center;
}
.marquee-content {
    white-space: nowrap;
    display: inline-block;
    animation: marquee 15s linear infinite;
    animation-delay: 0s;
    font-size: 1.2rem;
}
@keyframes marquee {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}
/* Override: User Guide Modal Grid Layout */
.guide-modal {
/* Introduction Page Layout Override */
#guideModal .guide-modal {
    width: 100vw !important;
    max-width: none !important;
    height: auto !important;
}
#guideModal .guide-modal .guide-body {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    grid-template-rows: auto auto !important;
    gap: 1.5rem !important;
}
#guideModal .guide-modal .guide-body .guide-content {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 1.5rem !important;
    grid-column: 1 / -1 !important;
}
#guideModal .guide-modal .guide-body .guide-actions {
    grid-column: 1 / -1 !important;
    justify-self: center !important;
}
/* INTRODUCTION PAGE LAYOUT OVERRIDE */
#guideModal .guide-modal {
    width: 100vw !important;
    max-width: none !important;
    height: auto !important;
}

#guideModal .guide-modal .guide-body .guide-content {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    grid-auto-flow: column !important;
    grid-template-rows: repeat(3, auto) auto !important;
    gap: 1.5rem !important;
}

#guideModal .guide-modal .guide-body .guide-content > .guide-actions {
    grid-column: 1 / -1 !important;
    justify-self: center !important;
}
/* INTRODUCTION PAGE LAYOUT OVERRIDE */
.guide-modal {
    width: 100vw !important;
    max-width: none !important;
    height: auto !important;
}

.guide-modal .guide-body {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    grid-template-rows: auto auto !important;
    gap: 1.5rem !important;
}

.guide-modal .guide-body .guide-content {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    grid-auto-rows: auto !important;
    gap: 1.5rem !important;
    grid-column: 1 / -1 !important;
    grid-row: 1 !important;
}

.guide-modal .guide-body .guide-actions {
    grid-column: 1 / -1 !important;
    grid-row: 2 !important;
    justify-self: center !important;
}
    display: grid !important;
    grid-template-columns: 1fr !important;
    grid-template-rows: auto 1fr auto !important;
}
.guide-modal > .guide-header,
.guide-modal > .guide-body,
.guide-modal > .guide-actions {
    grid-column: 1 / -1 !important;
}
/* Override: Place introduction panels and actions */
.guide-modal .guide-body .guide-content {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    grid-template-rows: repeat(3, auto) auto !important;
    gap: 1.5rem !important;
}
.guide-modal .guide-body .guide-content > .guide-actions {
    grid-column: 1 / -1 !important;
}
/* INTRODUCTION PAGE CUSTOM OVERRIDES */
.guide-modal {
    width: 60vw !important;
    height: 60vh !important;
    max-width: none !important;
    max-height: none !important;
}

.guide-modal .guide-body {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    grid-template-rows: auto auto auto !important;
    gap: 1.5rem !important;
}

.guide-modal .guide-body .guide-content {
    display: contents !important;
}

.guide-modal .guide-body .guide-actions {
    grid-column: 1 / -1 !important;
    grid-row: 3 !important;
    justify-self: center !important;
}