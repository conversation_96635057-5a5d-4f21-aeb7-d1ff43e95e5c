#!/usr/bin/env node

/**
 * Test script for PayPal Agent Toolkit MCP Server
 * This script demonstrates how to use the PayPal MCP server tools
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('PayPal MCP Server Test Script');
console.log('=============================\n');

// Check if mcp_settings.json exists
const settingsPath = path.join(__dirname, 'mcp_settings.json');
if (!fs.existsSync(settingsPath)) {
    console.error('❌ mcp_settings.json not found!');
    console.log('Please run setup-paypal-mcp.bat first.');
    process.exit(1);
}

// Read and validate settings
let settings;
try {
    const settingsContent = fs.readFileSync(settingsPath, 'utf8');
    settings = JSON.parse(settingsContent);
    console.log('✅ Found mcp_settings.json');
} catch (error) {
    console.error('❌ Error reading mcp_settings.json:', error.message);
    process.exit(1);
}

// Check PayPal server configuration
const paypalServer = settings.mcpServers['github.com/paypal/agent-toolkit'];
if (!paypalServer) {
    console.error('❌ PayPal server not configured in mcp_settings.json');
    process.exit(1);
}

console.log('✅ PayPal server configuration found');

// Check access token
const accessToken = paypalServer.env.PAYPAL_ACCESS_TOKEN;
if (!accessToken || accessToken === 'YOUR_PAYPAL_ACCESS_TOKEN') {
    console.log('⚠️  Access token not configured');
    console.log('Please update PAYPAL_ACCESS_TOKEN in mcp_settings.json');
    console.log('See PAYPAL_MCP_SETUP.md for instructions on generating an access token.\n');
} else {
    console.log('✅ Access token configured');
}

// Test PayPal MCP package availability
console.log('\nTesting PayPal MCP package...');

const testProcess = spawn('npx', ['-y', '@paypal/mcp', '--help'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    shell: true
});

let output = '';
let errorOutput = '';

testProcess.stdout.on('data', (data) => {
    output += data.toString();
});

testProcess.stderr.on('data', (data) => {
    errorOutput += data.toString();
});

testProcess.on('close', (code) => {
    if (code === 0) {
        console.log('✅ PayPal MCP package is available');
        console.log('\nAvailable tools:');
        console.log('- Invoice management (create, list, send, cancel)');
        console.log('- Payment processing (create orders, process payments)');
        console.log('- Dispute management');
        console.log('- Shipment tracking');
        console.log('- Catalog management');
        console.log('- Subscription management');
        console.log('- Transaction reporting');
        
        if (accessToken && accessToken !== 'YOUR_PAYPAL_ACCESS_TOKEN') {
            console.log('\n🎉 Setup complete! The PayPal MCP server is ready to use.');
            console.log('\nTo test a tool, you can run:');
            console.log('npx -y @paypal/mcp --tools=all');
        } else {
            console.log('\n⚠️  Setup incomplete: Please configure your access token.');
        }
    } else {
        console.error('❌ PayPal MCP package test failed');
        console.error('Error output:', errorOutput);
        console.log('\nPlease ensure:');
        console.log('1. Node.js is installed (node --version)');
        console.log('2. Internet connection is available');
        console.log('3. npm/npx is working correctly');
    }
});

testProcess.on('error', (error) => {
    console.error('❌ Failed to run test:', error.message);
    console.log('\nPlease ensure Node.js and npm are properly installed.');
});