/**
 * Google Maps MCP Server Test Script
 * 
 * This script demonstrates how to use the Google Maps MCP server tools
 * once the server is properly connected and configured with a valid API key.
 */

// Example usage of Google Maps MCP tools
const googleMapsMCPExamples = {
    
    // 1. Geocoding - Convert address to coordinates
    geocodeExample: {
        tool: "maps_geocode",
        input: {
            address: "1600 Amphitheatre Parkway, Mountain View, CA"
        },
        expectedOutput: {
            location: { lat: 37.4224764, lng: -122.0842499 },
            formatted_address: "1600 Amphitheatre Pkwy, Mountain View, CA 94043, USA",
            place_id: "ChIJ2eUgeAK6j4ARbn5u_wAGqWA"
        }
    },

    // 2. Reverse Geocoding - Convert coordinates to address
    reverseGeocodeExample: {
        tool: "maps_reverse_geocode",
        input: {
            latitude: 37.4224764,
            longitude: -122.0842499
        },
        expectedOutput: {
            formatted_address: "1600 Amphitheatre Pkwy, Mountain View, CA 94043, USA",
            place_id: "ChIJ2eUgeAK6j4ARbn5u_wAGqWA",
            address_components: [
                { long_name: "1600", short_name: "1600", types: ["street_number"] },
                { long_name: "Amphitheatre Parkway", short_name: "Amphitheatre Pkwy", types: ["route"] }
            ]
        }
    },

    // 3. Places Search - Search for places
    searchPlacesExample: {
        tool: "maps_search_places",
        input: {
            query: "restaurants near Times Square",
            location: { latitude: 40.7580, longitude: -73.9855 },
            radius: 1000
        },
        expectedOutput: [
            {
                name: "Restaurant Name",
                address: "123 Broadway, New York, NY",
                location: { lat: 40.7580, lng: -73.9855 }
            }
        ]
    },

    // 4. Place Details - Get detailed place information
    placeDetailsExample: {
        tool: "maps_place_details",
        input: {
            place_id: "ChIJ2eUgeAK6j4ARbn5u_wAGqWA"
        },
        expectedOutput: {
            name: "Googleplex",
            address: "1600 Amphitheatre Pkwy, Mountain View, CA 94043",
            phone: "******-253-0000",
            rating: 4.4,
            opening_hours: {
                open_now: true,
                weekday_text: ["Monday: Open 24 hours", "Tuesday: Open 24 hours"]
            }
        }
    },

    // 5. Distance Matrix - Calculate distances between points
    distanceMatrixExample: {
        tool: "maps_distance_matrix",
        input: {
            origins: ["New York, NY", "Los Angeles, CA"],
            destinations: ["Chicago, IL", "Miami, FL"],
            mode: "driving"
        },
        expectedOutput: {
            rows: [
                {
                    elements: [
                        { distance: { text: "790 mi", value: 1271802 }, duration: { text: "12 hours 30 mins", value: 45000 } }
                    ]
                }
            ]
        }
    },

    // 6. Elevation - Get elevation data
    elevationExample: {
        tool: "maps_elevation",
        input: {
            locations: [
                { latitude: 39.7391536, longitude: -104.9847034 }, // Denver, CO
                { latitude: 36.1699412, longitude: -115.1398296 }  // Las Vegas, NV
            ]
        },
        expectedOutput: [
            { elevation: 1655.637939453125, location: { lat: 39.7391536, lng: -104.9847034 } },
            { elevation: 664.9069213867188, location: { lat: 36.1699412, lng: -115.1398296 } }
        ]
    },

    // 7. Directions - Get directions between points
    directionsExample: {
        tool: "maps_directions",
        input: {
            origin: "San Francisco, CA",
            destination: "Los Angeles, CA",
            mode: "driving"
        },
        expectedOutput: {
            routes: [
                {
                    legs: [
                        {
                            distance: { text: "383 mi", value: 616463 },
                            duration: { text: "5 hours 48 mins", value: 20897 },
                            steps: [
                                {
                                    html_instructions: "Head south on US-101 S",
                                    distance: { text: "0.2 mi", value: 322 },
                                    duration: { text: "1 min", value: 60 }
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    }
};

// Function to test MCP connection (when server is connected)
async function testGoogleMapsMCP() {
    console.log("Google Maps MCP Server Test Examples");
    console.log("=====================================");
    
    for (const [exampleName, example] of Object.entries(googleMapsMCPExamples)) {
        console.log(`\n${exampleName}:`);
        console.log(`Tool: ${example.tool}`);
        console.log(`Input:`, JSON.stringify(example.input, null, 2));
        console.log(`Expected Output:`, JSON.stringify(example.expectedOutput, null, 2));
        console.log("---");
    }
    
    console.log("\nTo use these tools:");
    console.log("1. Ensure you have a valid Google Maps API key");
    console.log("2. Update the API key in mcp_settings.json");
    console.log("3. Restart VS Code to connect the MCP server");
    console.log("4. Use the use_mcp_tool command with the server name and tool");
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { googleMapsMCPExamples, testGoogleMapsMCP };
}

// Run test if executed directly
if (typeof require !== 'undefined' && require.main === module) {
    testGoogleMapsMCP();
}