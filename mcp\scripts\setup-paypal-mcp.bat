@echo off
echo Setting up PayPal Agent Toolkit MCP Server...
echo.

echo Step 1: Checking for Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js is not installed. Please install Node.js first.
    echo.
    echo 1. Go to https://nodejs.org/
    echo 2. Download the LTS version for Windows
    echo 3. Run the installer and follow the instructions
    echo 4. Restart your command prompt/terminal
    echo 5. Run this script again
    echo.
    pause
    exit /b 1
)

echo Node.js is installed!
node --version
echo.

echo Step 2: Installing PayPal Agent Toolkit MCP package...
npx -y @paypal/mcp --help
if %errorlevel% neq 0 (
    echo Failed to install or run PayPal MCP package.
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)

echo.
echo Step 3: MCP Server setup complete!
echo.
echo The MCP server configuration has been created in mcp_settings.json
echo.
echo To use the server, you need to:
echo 1. Get a PayPal Access Token from https://developer.paypal.com/dashboard/
echo 2. Update the PAYPAL_ACCESS_TOKEN in mcp_settings.json
echo 3. Set PAYPAL_ENVIRONMENT to either "SANDBOX" or "PRODUCTION"
echo.
echo Available tools include:
echo - Invoice management (create, list, send, cancel)
echo - Payment processing (create orders, process payments)
echo - Dispute management
echo - Shipment tracking
echo - Catalog management
echo - Subscription management
echo - Transaction reporting
echo.
pause