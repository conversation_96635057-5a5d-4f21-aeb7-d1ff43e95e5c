/**
 * Tooltip functionality for the KMS Rental Grid application
 */

import { getElement, showElement, hideElement, setHTML } from '../core/dom.js';
import { getMockEstateData } from '../data/mock.js';
import { getLocationState } from '../data/state.js';

// Cache tooltip elements
let previewTooltip, tooltipImage, tooltipInfo;

// Tooltip tracking variables
let hoveredItemId = null;

/**
 * Initialize tooltip elements
 */
export function initializeTooltip() {
    previewTooltip = getElement('previewTooltip');
    tooltipImage = getElement('tooltipImage');
    tooltipInfo = getElement('tooltipInfo');
}

/**
 * Show the preview tooltip
 * @param {Event} event - The mouse event
 * @param {string} innerGridId - The inner grid ID
 */
export function showPreview(event, innerGridId) {
    if (!previewTooltip || !tooltipImage || !tooltipInfo) {
        initializeTooltip();
    }
    
    // Set hovered item ID
    hoveredItemId = innerGridId;
    
    const currentLocation = getLocationState();
    const locationId = `${currentLocation.category}-${currentLocation.gridId}-${innerGridId}`;
    
    // Get estate data for this location
    const estateData = getMockEstateData(currentLocation.category, currentLocation.gridId, innerGridId);
    
    // Update tooltip image
    if (tooltipImage) {
        tooltipImage.src = estateData.image;
    }
    
    // Update tooltip info
    if (tooltipInfo) {
        const infoHTML = `
            <div class="tooltip-title">${estateData.name}</div>
            <div class="tooltip-price">${estateData.price.toLocaleString()} KMS Coins</div>
            <div class="tooltip-owner">Owner: ${estateData.owner}</div>
            <div class="tooltip-location">Location: ${locationId}</div>
        `;
        setHTML(tooltipInfo, infoHTML);
    }
    
    // Position and show tooltip
    updateTooltipPosition(event);
    showElement(previewTooltip, 'block');
}

/**
 * Hide the preview tooltip
 */
export function hidePreview() {
    if (previewTooltip) {
        hideElement(previewTooltip);
    }
    hoveredItemId = null;
}

/**
 * Update the tooltip position based on mouse coordinates
 * @param {Event} event - The mouse event
 */
export function updateTooltipPosition(event) {
    if (!previewTooltip || hoveredItemId === null) return;
    
    const mouseX = event.clientX;
    const mouseY = event.clientY;
    
    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // Get tooltip dimensions
    const tooltipWidth = previewTooltip.offsetWidth;
    const tooltipHeight = previewTooltip.offsetHeight;
    
    // Calculate position (keep tooltip within viewport)
    let posX = mouseX + 15; // Offset from cursor
    let posY = mouseY + 15;
    
    // Adjust if tooltip would go off-screen
    if (posX + tooltipWidth > viewportWidth) {
        posX = mouseX - tooltipWidth - 15;
    }
    
    if (posY + tooltipHeight > viewportHeight) {
        posY = mouseY - tooltipHeight - 15;
    }
    
    // Apply position
    previewTooltip.style.left = `${posX}px`;
    previewTooltip.style.top = `${posY}px`;
}
