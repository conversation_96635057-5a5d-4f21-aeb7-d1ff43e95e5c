# PayPal Agent Toolkit MCP Server Setup

This guide will help you set up and use the PayPal Agent Toolkit MCP server in your project.

## Prerequisites

1. **Node.js 18+** - Required for running the MCP server
2. **PayPal Developer Account** - For API credentials
3. **MCP-compatible client** - Such as <PERSON>, <PERSON><PERSON>, or <PERSON>urs<PERSON>

## Installation Steps

### 1. Install Node.js

If Node.js is not installed on your system:

1. Visit [Node.js official website](https://nodejs.org/)
2. Download the LTS version for Windows
3. Run the installer and follow the instructions
4. Restart your command prompt/terminal
5. Verify installation: `node --version`

### 2. Run the Setup Script

Execute the setup script to install the PayPal MCP package:

```bash
setup-paypal-mcp.bat
```

### 3. Get PayPal API Credentials

1. Go to [PayPal Developer Dashboard](https://developer.paypal.com/dashboard/)
2. Sign up or log in with your PayPal credentials
3. Click on **Apps & Credentials**
4. Create a new app or select an existing one
5. Copy your **Client ID** and **Client Secret**

### 4. Generate Access Token

Use one of these methods to generate an access token:

#### Using cURL:
```bash
curl -v https://api-m.sandbox.paypal.com/v1/oauth2/token \
  -H "Accept: application/json" \
  -H "Accept-Language: en_US" \
  -u "CLIENT_ID:CLIENT_SECRET" \
  -d "grant_type=client_credentials"
```

#### Using PowerShell:
```powershell
$clientId = "YOUR_CLIENT_ID"
$clientSecret = "YOUR_CLIENT_SECRET"
$credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${clientId}:${clientSecret}"))

$headers = @{
    "Authorization" = "Basic $credentials"
    "Accept" = "application/json"
    "Accept-Language" = "en_US"
}

$body = "grant_type=client_credentials"

$response = Invoke-RestMethod -Uri "https://api-m.sandbox.paypal.com/v1/oauth2/token" -Method Post -Headers $headers -Body $body -ContentType "application/x-www-form-urlencoded"

Write-Output "Access Token: $($response.access_token)"
```

### 5. Update Configuration

Edit [`mcp_settings.json`](mcp_settings.json:8) and replace `YOUR_PAYPAL_ACCESS_TOKEN` with your actual access token:

```json
{
  "mcpServers": {
    "github.com/paypal/agent-toolkit": {
      "command": "npx",
      "args": [
        "-y",
        "@paypal/mcp",
        "--tools=all"
      ],
      "env": {
        "PAYPAL_ACCESS_TOKEN": "YOUR_ACTUAL_ACCESS_TOKEN_HERE",
        "PAYPAL_ENVIRONMENT": "SANDBOX"
      }
    }
  }
}
```

## Available Tools

The PayPal Agent Toolkit provides these tools:

### Invoice Management
- [`create_invoice`](https://github.com/paypal/agent-toolkit:1) - Create a new invoice
- [`list_invoices`](https://github.com/paypal/agent-toolkit:1) - List invoices with pagination
- [`get_invoice`](https://github.com/paypal/agent-toolkit:1) - Get invoice details
- [`send_invoice`](https://github.com/paypal/agent-toolkit:1) - Send invoice to recipients
- [`send_invoice_reminder`](https://github.com/paypal/agent-toolkit:1) - Send invoice reminder
- [`cancel_sent_invoice`](https://github.com/paypal/agent-toolkit:1) - Cancel a sent invoice
- [`generate_invoice_qr_code`](https://github.com/paypal/agent-toolkit:1) - Generate QR code for invoice

### Payment Processing
- [`create_order`](https://github.com/paypal/agent-toolkit:1) - Create a PayPal order
- [`get_order`](https://github.com/paypal/agent-toolkit:1) - Retrieve order details
- [`pay_order`](https://github.com/paypal/agent-toolkit:1) - Process payment for order

### Dispute Management
- [`list_disputes`](https://github.com/paypal/agent-toolkit:1) - List open disputes
- [`get_dispute`](https://github.com/paypal/agent-toolkit:1) - Get dispute details
- [`accept_dispute_claim`](https://github.com/paypal/agent-toolkit:1) - Accept dispute claim

### Shipment Tracking
- [`create_shipment_tracking`](https://github.com/paypal/agent-toolkit:1) - Create tracking record
- [`get_shipment_tracking`](https://github.com/paypal/agent-toolkit:1) - Get tracking info

### Catalog Management
- [`create_product`](https://github.com/paypal/agent-toolkit:1) - Create new product
- [`list_products`](https://github.com/paypal/agent-toolkit:1) - List products
- [`show_product_details`](https://github.com/paypal/agent-toolkit:1) - Get product details

### Subscription Management
- [`create_subscription_plan`](https://github.com/paypal/agent-toolkit:1) - Create subscription plan
- [`list_subscription_plans`](https://github.com/paypal/agent-toolkit:1) - List plans
- [`show_subscription_plan_details`](https://github.com/paypal/agent-toolkit:1) - Get plan details
- [`create_subscription`](https://github.com/paypal/agent-toolkit:1) - Create subscription
- [`show_subscription_details`](https://github.com/paypal/agent-toolkit:1) - Get subscription details
- [`cancel_subscription`](https://github.com/paypal/agent-toolkit:1) - Cancel subscription

### Reporting
- [`list_transactions`](https://github.com/paypal/agent-toolkit:1) - List transactions with filtering

## Testing the Setup

Once configured, you can test the MCP server by running:

```bash
npx -y @paypal/mcp --tools=all
```

This should start the server and show available tools.

## Integration with MCP Clients

### Claude Desktop
Add the configuration to `~/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "paypal": {
      "command": "npx",
      "args": ["-y", "@paypal/mcp", "--tools=all"],
      "env": {
        "PAYPAL_ACCESS_TOKEN": "YOUR_ACCESS_TOKEN",
        "PAYPAL_ENVIRONMENT": "SANDBOX"
      }
    }
  }
}
```

### Other MCP Clients
Use the [`mcp_settings.json`](mcp_settings.json:1) configuration file with your preferred MCP client.

## Environment Variables

- `PAYPAL_ACCESS_TOKEN`: Your PayPal access token
- `PAYPAL_ENVIRONMENT`: Set to `SANDBOX` for testing or `PRODUCTION` for live transactions

## Security Notes

- Keep your access tokens secure and never commit them to version control
- Use environment variables for production deployments
- Tokens expire after 3-8 hours and need to be refreshed
- Use sandbox environment for testing

## Troubleshooting

1. **Node.js not found**: Ensure Node.js is installed and in your PATH
2. **npx command fails**: Restart your terminal after installing Node.js
3. **Access token invalid**: Generate a new token using the steps above
4. **Network errors**: Check your internet connection and firewall settings

## Support

For issues with the PayPal Agent Toolkit, visit:
- [GitHub Repository](https://github.com/paypal/agent-toolkit)
- [PayPal Developer Documentation](https://developer.paypal.com/)