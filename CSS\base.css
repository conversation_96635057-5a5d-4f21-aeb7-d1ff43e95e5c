/* ===========
   Base Styles - 基礎樣式
   =========== */

/* 基本重置和全域樣式 */
* {
    box-sizing: border-box;
}

body {
    width: 100vw;
    height: 100vh;
    background-image: url("/BG/KMS_Nature/KMS_Nature_001.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    background-attachment: fixed;
    overflow: hidden;
    margin: 0; /* 去除預設 margin */
    font-family: Arial, sans-serif;
}

/* 基本容器樣式 */
.grid-container {
    display: grid;
    grid-template-columns: repeat(20, 1fr);
    grid-template-rows: repeat(12, 1fr);
    gap: 2px;
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    padding: 15px;
}

/* 基本文字樣式 */
.title-3d {
    position: absolute;
    top: -20%;
    font-size: 38px;
    color: rgb(0, 255, 225);
    text-align: center;
    padding: 10px 0;
    letter-spacing: 3px;
    font-family: <PERSON><PERSON>, sans-serif;
    text-shadow:
        0 1px 0 #2d2c2c,
        0 2px 0 #5b5a5a,
        0 3px 0 #595858,
        0 4px 0 #302f2f,
        0 5px 0 #1a1a1a,
        0 6px 1px rgba(0,0,0,.1),
        0 0 5px rgba(0,0,0,.1),
        0 1px 3px rgba(0,0,0,.3),
        0 3px 5px rgba(0,0,0,.2),
        0 5px 10px rgba(0,0,0,.25),
        0 10px 10px rgba(0,0,0,.2),
        0 20px 20px rgba(0,0,0,.15);
    transform: perspective(500px) rotateX(10deg);
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 搜尋列樣式 */
.search-container {
    z-index: 11;
    width: 90%;
    height: 36px;
    padding: 0;
    margin-top: 4px;
    position: absolute;
    bottom: 8px;
}

.search-bar {
    display: flex;
    width: 100%;
    height: 100%;
    background: rgba(30, 33, 40, 0.95);
    border-radius: 20px;
    overflow: hidden;
    border: 2px solid rgba(52, 235, 213, 0.9);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.search-bar:hover, .search-bar:focus-within {
    background: rgba(20, 23, 30, 0.98);
    border-color: rgba(52, 235, 213, 1);
    box-shadow: 0 4px 15px rgba(52, 235, 213, 0.4);
}

.search-bar input {
    flex: 1;
    height: 100%;
    padding: 0 15px;
    border: none;
    background: transparent;
    color: white;
    font-size: 14px;
    outline: none;
    font-weight: 500;
}

.search-bar input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-bar button {
    width: 36px;
    height: 100%;
    border: none;
    background: rgba(52, 235, 213, 1);
    color: #111;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background 0.2s;
}

.search-bar button:hover {
    background: rgba(82, 255, 233, 1);
}

.search-icon {
    font-size: 18px;
}

/* Logo 容器調整 */
.logo-container {
    z-index: 11;
    overflow: visible;
    display: flex;
    flex-direction: column;
    position: relative;
}

.logo-container .toggle-button {
    position: relative;
}

/* 幫助按鈕樣式 */
.help-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 215, 0, 0.9);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.help-button:hover {
    background: rgba(255, 215, 0, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.help-icon {
    font-size: 18px;
    color: #333;
}

/* 基本圖標樣式 */
.btn-icon {
    font-size: 20px;
}
