/**
 * Search functionality for the KMS Rental Grid application
 */

import { getElement, addEvent } from '../core/dom.js';
import { searchData } from '../data/mock.js';
import { showSearchResults } from '../ui/modal.js';

// Cache search elements
let searchInput, searchButton;

/**
 * Initialize search functionality
 */
export function initializeSearch() {
    searchInput = getElement('globalSearch');
    searchButton = getElement('searchButton');
    
    if (!searchInput || !searchButton) {
        console.error('搜索元素未找到');
        return;
    }
    
    // Search button click event
    addEvent(searchButton, 'click', () => {
        performSearch(searchInput.value);
    });
    
    // Enter key press in search input
    addEvent(searchInput, 'keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch(searchInput.value);
        }
    });
}

/**
 * Perform search with the given query
 * @param {string} query - The search query
 */
function performSearch(query) {
    if (!query.trim()) {
        alert('Please enter search keywords');
        return;
    }
    
    console.log('搜索關鍵詞:', query);
    
    // Filter search data based on query
    const results = {
        users: searchData.users.filter(user => user.includes(query)),
        estates: searchData.estates.filter(estate => estate.includes(query)),
        topics: searchData.topics.filter(topic => topic.includes(query))
    };
    
    // Show search results
    showSearchResults(results, query);
}
