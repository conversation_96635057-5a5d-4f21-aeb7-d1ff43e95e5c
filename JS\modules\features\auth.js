/**
 * Authentication functionality for the KMS Rental Grid application
 */

import { getElement, getElements, addEvent, showElement, hideElement } from '../core/dom.js';
import { isValidEmail, isValidPassword } from '../core/utils.js';

// Cache auth elements
let loginBtn, registerBtn, loginModal, registerModal, forgotPasswordModal, forgotPasswordLink;
let loginForm, loginErrorMsg, registerForm, passwordMatchErrorMsg, forgotPasswordForm;
let switchToLoginLink, switchToRegisterLink, backToLoginLink;

/**
 * Initialize authentication functionality
 */
export function initializeAuth() {
    // Get auth elements
    loginBtn = getElement('loginBtn');
    registerBtn = getElement('registerBtn');
    loginModal = getElement('loginModal');
    registerModal = getElement('registerModal');
    forgotPasswordModal = getElement('forgotPasswordModal');
    forgotPasswordLink = getElement('forgotPasswordLink');

    // Get form elements
    loginForm = getElement('loginForm');
    loginErrorMsg = getElement('loginErrorMsg');
    registerForm = getElement('registerForm');
    passwordMatchErrorMsg = getElement('passwordMatchErrorMsg');
    forgotPasswordForm = getElement('forgotPasswordForm');

    // Get navigation links
    switchToLoginLink = getElement('switchToLogin');
    switchToRegisterLink = getElement('switchToRegister');
    backToLoginLink = getElement('backToLogin');

    // Get close buttons
    const closeButtons = getElements('.auth-close-btn');

    // Initialize event listeners
    initializeAuthButtons();
    initializeLoginForm();
    initializeRegisterForm();
    initializeForgotPasswordForm();
    initializePasswordToggle();
    initializeFormNavigation();
    initializePasswordStrengthMeter();
    initializeRegisterProgress();
}

/**
 * Initialize authentication buttons
 */
function initializeAuthButtons() {
    // Login button
    if (loginBtn) {
        addEvent(loginBtn, 'click', function() {
            showElement(loginModal, 'flex');
        });
    }

    // Register button
    if (registerBtn) {
        addEvent(registerBtn, 'click', function() {
            showElement(registerModal, 'flex');
        });
    }

    // Forgot password link
    if (forgotPasswordLink) {
        addEvent(forgotPasswordLink, 'click', function(e) {
            e.preventDefault();
            hideElement(loginModal);
            showElement(forgotPasswordModal, 'flex');
        });
    }

    // Close buttons
    const closeButtons = getElements('.auth-close-btn');
    closeButtons.forEach(button => {
        addEvent(button, 'click', function() {
            hideElement(loginModal);
            hideElement(registerModal);
            hideElement(forgotPasswordModal);
        });
    });
}

/**
 * Initialize login form
 */
function initializeLoginForm() {
    if (loginForm && loginErrorMsg) {
        addEvent(loginForm, 'submit', function(e) {
            e.preventDefault();
            const username = getElement('loginUsername').value;
            const password = getElement('loginPassword').value;

            // This should be backend validation logic
            // In a real application, use fetch API or AJAX for backend validation
            if (username === "demo" && password === "Password123!") {
                // Login success
                loginErrorMsg.classList.remove("visible");
                alert("Login successful!");
                hideElement(loginModal);
            } else {
                // Login failure
                loginErrorMsg.textContent = "Incorrect username or password, please try again";
                loginErrorMsg.classList.add("visible");
            }
        });
    }
}

/**
 * Initialize register form
 */
function initializeRegisterForm() {
    if (registerForm) {
        // Password validation
        const passwordInput = getElement('password');
        const confirmPasswordInput = getElement('confirmPassword');
        const passwordRequirements = getElement('passwordRequirements');
        const emailInput = getElement('email');
        const emailErrorMsg = getElement('emailErrorMsg');
        const passwordStrengthMeter = document.querySelector('.password-strength-meter');
        const passwordStrengthValue = document.querySelector('.password-strength-value');

        if (passwordInput && confirmPasswordInput && passwordMatchErrorMsg) {
            // Validate password as user types
            addEvent(passwordInput, 'input', function() {
                const password = this.value;

                // Update password strength meter
                updatePasswordStrength(password, passwordStrengthMeter, passwordStrengthValue);

                if (password && !isValidPassword(password)) {
                    passwordRequirements.classList.add('visible');
                    passwordRequirements.classList.add('invalid');
                    passwordRequirements.classList.remove('valid');
                } else if (password) {
                    passwordRequirements.classList.remove('visible');
                    passwordRequirements.classList.remove('invalid');
                    passwordRequirements.classList.add('valid');
                } else {
                    passwordRequirements.classList.remove('visible');
                    passwordRequirements.classList.remove('valid');
                    passwordRequirements.classList.remove('invalid');
                }

                // Check if passwords match
                if (confirmPasswordInput.value && confirmPasswordInput.value !== password) {
                    passwordMatchErrorMsg.textContent = "Passwords do not match";
                    passwordMatchErrorMsg.classList.add('visible');
                } else {
                    passwordMatchErrorMsg.classList.remove('visible');
                }
            });

            // Check password match as user types in confirm field
            addEvent(confirmPasswordInput, 'input', function() {
                if (this.value && this.value !== passwordInput.value) {
                    passwordMatchErrorMsg.textContent = "密碼不匹配";
                    passwordMatchErrorMsg.classList.add('visible');
                } else {
                    passwordMatchErrorMsg.classList.remove('visible');
                }
            });
        }

        // Email validation
        if (emailInput && emailErrorMsg) {
            addEvent(emailInput, 'input', function() {
                if (this.value && !isValidEmail(this.value)) {
                    emailErrorMsg.textContent = "Please enter a valid email address";
                    emailErrorMsg.classList.add('visible');
                } else {
                    emailErrorMsg.classList.remove('visible');
                }
            });

            addEvent(emailInput, 'blur', function() {
                if (this.value && !isValidEmail(this.value)) {
                    emailErrorMsg.textContent = "請輸入有效的電子郵件地址";
                    emailErrorMsg.classList.add('visible');
                } else {
                    emailErrorMsg.classList.remove('visible');
                }
            });
        }

        // Form field animations
        const formControls = registerForm.querySelectorAll('.form-control');
        formControls.forEach(control => {
            addEvent(control, 'focus', function() {
                this.parentElement.classList.add('focused');
            });

            addEvent(control, 'blur', function() {
                this.parentElement.classList.remove('focused');

                // Add 'filled' class if the field has a value
                if (this.value) {
                    this.classList.add('filled');
                } else {
                    this.classList.remove('filled');
                }
            });
        });

        // Form submission
        addEvent(registerForm, 'submit', function(e) {
            e.preventDefault();

            // Validate email
            const email = emailInput.value;
            if (!isValidEmail(email)) {
                emailErrorMsg.textContent = "請輸入有效的電子郵件地址";
                emailErrorMsg.classList.add('visible');
                return;
            }

            // Validate password
            const password = passwordInput.value;
            if (!isValidPassword(password)) {
                passwordRequirements.classList.add('visible');
                passwordRequirements.classList.add('invalid');
                return;
            }

            // Validate password match
            if (password !== confirmPasswordInput.value) {
                passwordMatchErrorMsg.textContent = "密碼不匹配";
                passwordMatchErrorMsg.classList.add('visible');
                return;
            }

            // Update progress steps
            const progressSteps = document.querySelectorAll('.progress-step');
            progressSteps.forEach(step => step.classList.add('completed'));

            // Collect all registration data
            const userData = {
                firstName: getElement('firstName').value,
                lastName: getElement('lastName').value,
                birthdate: getElement('birthdate').value,
                gender: getElement('gender').value,
                language: getElement('language').value,
                email: email,
                password: password
            };

            // This should be backend registration logic
            // In a real application, use fetch API or AJAX to send data to backend
            console.log("註冊資料：", userData);

            // Show success animation
            const authBtn = registerForm.querySelector('.auth-btn');
            authBtn.innerHTML = '<span style="margin-right: 8px;">✓</span> 註冊成功！';
            authBtn.style.backgroundColor = '#27ae60';

            // Redirect to login after delay
            setTimeout(() => {
                hideElement(registerModal);
                showElement(loginModal, 'flex');
            }, 1500);
        });
    }
}

/**
 * Initialize forgot password form
 */
function initializeForgotPasswordForm() {
    if (forgotPasswordForm) {
        const resetEmailInput = getElement('resetEmail');
        const resetEmailErrorMsg = getElement('resetEmailErrorMsg');
        const forgotPasswordErrorMsg = getElement('forgotPasswordErrorMsg');
        const forgotPasswordSuccessMsg = getElement('forgotPasswordSuccessMsg');

        // Email validation
        if (resetEmailInput && resetEmailErrorMsg) {
            addEvent(resetEmailInput, 'blur', function() {
                if (this.value && !isValidEmail(this.value)) {
                    resetEmailErrorMsg.textContent = "請輸入有效的電子郵件地址";
                    resetEmailErrorMsg.classList.add('visible');
                } else {
                    resetEmailErrorMsg.classList.remove('visible');
                }
            });
        }

        // Form submission
        addEvent(forgotPasswordForm, 'submit', function(e) {
            e.preventDefault();

            // Validate email
            const email = resetEmailInput.value;
            if (!isValidEmail(email)) {
                resetEmailErrorMsg.textContent = "請輸入有效的電子郵件地址";
                resetEmailErrorMsg.classList.add('visible');
                return;
            }

            // This should be backend logic to send reset email
            // In a real application, use fetch API or AJAX
            forgotPasswordSuccessMsg.textContent = `重設密碼連結已發送至 ${email}`;
            forgotPasswordSuccessMsg.style.display = 'block';
            forgotPasswordErrorMsg.style.display = 'none';

            // Reset form
            forgotPasswordForm.reset();

            // Close modal after 3 seconds
            setTimeout(() => {
                hideElement(forgotPasswordModal);
                forgotPasswordSuccessMsg.style.display = 'none';
            }, 3000);
        });
    }
}

/**
 * Initialize password toggle functionality
 */
function initializePasswordToggle() {
    const toggleButtons = getElements('.toggle-password');

    toggleButtons.forEach(button => {
        addEvent(button, 'click', function() {
            const passwordInput = this.previousElementSibling;

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                this.textContent = '👁️‍🗨️';
            } else {
                passwordInput.type = 'password';
                this.textContent = '👁️';
            }
        });
    });
}

/**
 * Initialize form navigation between login, register, and forgot password forms
 */
function initializeFormNavigation() {
    // Switch from register to login
    if (switchToLoginLink) {
        addEvent(switchToLoginLink, 'click', function(e) {
            e.preventDefault();
            hideElement(registerModal);
            showElement(loginModal, 'flex');
        });
    }

    // Switch from login to register
    if (switchToRegisterLink) {
        addEvent(switchToRegisterLink, 'click', function(e) {
            e.preventDefault();
            hideElement(loginModal);
            showElement(registerModal, 'flex');
        });
    }

    // Back to login from forgot password
    if (backToLoginLink) {
        addEvent(backToLoginLink, 'click', function(e) {
            e.preventDefault();
            hideElement(forgotPasswordModal);
            showElement(loginModal, 'flex');
        });
    }
}

/**
 * Initialize password strength meter
 */
function initializePasswordStrengthMeter() {
    const passwordInput = getElement('password');
    const passwordStrengthMeter = document.querySelector('.password-strength-meter');
    const passwordStrengthValue = document.querySelector('.password-strength-value');

    if (passwordInput && passwordStrengthMeter && passwordStrengthValue) {
        // Initial update
        updatePasswordStrength('', passwordStrengthMeter, passwordStrengthValue);
    }
}

/**
 * Update password strength meter based on password value
 * @param {string} password - The password to evaluate
 * @param {HTMLElement} meter - The password strength meter element
 * @param {HTMLElement} value - The password strength value element
 */
function updatePasswordStrength(password, meter, value) {
    if (!password) {
        meter.className = 'password-strength-meter';
        value.style.width = '0';
        return;
    }

    // Remove all strength classes
    meter.classList.remove('strength-weak', 'strength-medium', 'strength-strong', 'strength-very-strong');

    // Calculate password strength
    let strength = 0;

    // Length check
    if (password.length >= 8) strength += 1;
    if (password.length >= 10) strength += 1;

    // Character type checks
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    // Determine strength level
    if (strength <= 2) {
        meter.classList.add('strength-weak');
    } else if (strength <= 4) {
        meter.classList.add('strength-medium');
    } else if (strength <= 6) {
        meter.classList.add('strength-strong');
    } else {
        meter.classList.add('strength-very-strong');
    }
}

/**
 * Initialize register form progress steps
 */
function initializeRegisterProgress() {
    const progressSteps = document.querySelectorAll('.progress-step');
    const formControls = registerForm.querySelectorAll('.form-control');

    // Mark first step as active by default
    if (progressSteps.length > 0) {
        progressSteps[0].classList.add('active');
    }

    // Update progress as user fills out the form
    formControls.forEach(control => {
        addEvent(control, 'input', function() {
            updateRegisterProgress();
        });
    });
}

/**
 * Update register form progress based on filled fields
 */
function updateRegisterProgress() {
    const progressSteps = document.querySelectorAll('.progress-step');
    if (progressSteps.length === 0) return;

    const requiredFields = [
        'firstName', 'lastName', 'birthdate', 'gender', 'language',
        'email', 'password', 'confirmPassword'
    ];

    // Count filled fields
    let filledCount = 0;
    requiredFields.forEach(fieldId => {
        const field = getElement(fieldId);
        if (field && field.value) {
            filledCount++;
        }
    });

    // Calculate progress percentage
    const progressPercentage = Math.floor((filledCount / requiredFields.length) * 100);

    // Update progress steps
    if (progressPercentage < 33) {
        setActiveStep(progressSteps, 0);
    } else if (progressPercentage < 66) {
        setActiveStep(progressSteps, 1);
    } else if (progressPercentage < 100) {
        setActiveStep(progressSteps, 2);
    } else {
        // All fields filled
        progressSteps.forEach(step => {
            step.classList.add('active');
        });
    }
}

/**
 * Set the active progress step
 * @param {NodeList} steps - The progress step elements
 * @param {number} activeIndex - The index of the active step
 */
function setActiveStep(steps, activeIndex) {
    steps.forEach((step, index) => {
        if (index <= activeIndex) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
        // Remove completed class from all
        step.classList.remove('completed');
    });
}
