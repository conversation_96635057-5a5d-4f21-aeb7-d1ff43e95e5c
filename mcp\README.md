# MCP (Model Context Protocol) 檔案組織

這個資料夾包含所有與 MCP 相關的檔案，按照功能分類組織。

## 資料夾結構

### `/docs` - 文檔資料夾
包含所有 MCP 設置和說明文檔：
- `BROWSER_TOOLS_MCP_SETUP.md` - Browser Tools MCP 設置說明
- `FILESYSTEM_MCP_SETUP.md` - Filesystem MCP 設置說明
- `GOOGLE_MAPS_MCP_SETUP.md` - Google Maps MCP 設置說明
- `PAYPAL_MCP_SETUP.md` - PayPal MCP 設置說明
- `PUPPETEER_MCP_SETUP.md` - Puppeteer MCP 設置說明
- `SEQUENTIAL_THINKING_MCP_SETUP.md` - Sequential Thinking MCP 設置說明
- `README_MCP_SETUP.md` - MCP 總體設置說明
- `MCP_SETUP_STATUS.md` - MCP 設置狀態追蹤

### `/scripts` - 腳本資料夾
包含所有 MCP 設置和安裝腳本：
- `setup-browser-tools-mcp.bat` - <PERSON>rowser Tools MCP 安裝腳本
- `setup-paypal-mcp.bat` - PayPal MCP 安裝腳本
- `setup-paypal-mcp.ps1` - PayPal MCP PowerShell 安裝腳本

### `/tests` - 測試資料夾
包含所有 MCP 功能測試檔案：
- `test-filesystem-mcp.js` - Filesystem MCP 測試
- `test-google-maps-mcp.js` - Google Maps MCP 測試
- `test-paypal-mcp.js` - PayPal MCP 測試

## 根目錄檔案

以下檔案保留在專案根目錄，因為 MCP 系統運行時需要：
- `mcp_settings.json` - MCP servers 配置檔案
- `mcp-servers/` - MCP server 實際程式碼資料夾

## 使用說明

1. 查看文檔：參考 `/docs` 資料夾中的相關 `.md` 檔案
2. 執行設置：使用 `/scripts` 資料夾中的腳本檔案
3. 測試功能：執行 `/tests` 資料夾中的測試檔案
4. 配置修改：編輯根目錄的 `mcp_settings.json` 檔案