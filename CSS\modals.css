/* ===========
   Modals - 模態框樣式
   =========== */

/* 基本模態框樣式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.win-modal {
    width: 90vw;
    height: 90vh;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 14px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    backdrop-filter: blur(20px);
}

.titlebar {
    padding: 12px;
    background: rgba(0, 177, 212, 0.8);
    color: white;
    display: flex;
    justify-content: center; /* 標題置中 */
    align-items: center;
    position: relative; /* 為關閉按鈕定位 */
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    padding: 0 12px;
    border-radius: 12px;
    position: absolute;
    right: 10px;
}

.modal-content {
    padding: 20px;
    height: calc(100% - 60px);
    overflow: auto;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.modal-image {
    width: 50%;
    height: auto;
    max-height: 40vh;
    object-fit: cover;
    border-radius: 14px;
    margin: 0 auto 20px;
    display: block;
}

/* 預載(loading) 時的動畫 */
.modal-image.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.loading-text {
    color: #666;
    font-style: italic;
}

#modalText {
    text-align: center;
    width: 80%;
    margin: 0 auto;
}

#modalText h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: #fff;
}

#modalText p {
    font-size: 16px;
    line-height: 1.6;
    color: #fff;
    margin-bottom: 10px;
}

/* Tooltip */
#previewTooltip {
    position: absolute;
    width: 400px;
    height: 300px;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    display: none; /* 預設不顯示 */
    z-index: 2000; /* 在網格項目上層 */
    pointer-events: none; /* 滑鼠穿透，避免干擾 */
    padding: 8px;
}

#previewTooltip img {
    width: 100%;
    height: 70%;
    object-fit: cover;
    border-radius: 4px;
}

#tooltipInfo {
    margin-top: 6px;
    font-size: 14px;
    text-align: center;
}

/* User Guide Modal */
.guide-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(15px);
}

.guide-modal {
    width: 75vw;
    max-width: 1400px;
    max-height: 95vh;
    background: linear-gradient(135deg, rgba(20, 98, 209, 0.95), rgba(52, 235, 213, 0.95));
    border-radius: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    backdrop-filter: blur(20px);
    animation: guideModalSlideIn 0.5s ease-out;
    display: flex;
    flex-direction: column;
}

.guide-header {
    padding: 5px;
    background: linear-gradient(135deg, rgba(12, 36, 145, 0.9), rgba(20, 98, 209, 0.9));
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.guide-close-btn {
    position: absolute;
    right: 10px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.guide-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.guide-body {
    padding: 5px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Remove scrolling */
}

.guide-content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
    gap: 5px;
    flex: 1;
    overflow: hidden;
}

.guide-section {
    background: rgba(255, 255, 255, 0.15);
    padding: 5px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.guide-section:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 隱藏圖標 */
.guide-icon {
    display: none;
}

.guide-section h3 {
    color: white;
    font-size: 30px;
    margin-top: 5px;
    margin-bottom: 5px;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.guide-section p {
    color: rgba(255, 255, 255, 0.95);
    font-size: 24px;
    line-height: 1.2;
    text-align: center;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.guide-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 10px;
    padding: 5px;
    background: rgba(255, 255, 255, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
    margin-top: auto;
}

.guide-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 8px 15px;
    border: none;
    border-radius: 8px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    flex: 1;
    max-width: 30%;
}

.guide-btn-primary {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.guide-btn-primary:hover {
    background: linear-gradient(135deg, #ffed4e, #ffd700);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6);
}

.guide-btn-secondary {
    background: linear-gradient(135deg, #34ebd5, #00a8cc);
    color: white;
    box-shadow: 0 4px 15px rgba(52, 235, 213, 0.4);
}

.guide-btn-secondary:hover {
    background: linear-gradient(135deg, #00a8cc, #34ebd5);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 235, 213, 0.6);
}

.guide-btn-explore {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.guide-btn-explore:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
}

/* 服務內容樣式 */
.service-content {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    color: #fff;
    text-align: left;
}

.service-content h3 {
    font-size: 24px;
    margin-bottom: 20px;
    text-align: center;
}

.service-content h4 {
    font-size: 18px;
    margin-top: 25px;
    margin-bottom: 10px;
    color: #34ebd5;
}

.service-section, .terms-section {
    margin-bottom: 25px;
}

.service-section ul {
    list-style-type: disc;
    padding-left: 20px;
    margin: 10px 0;
}

.service-section ul li {
    margin-bottom: 5px;
}

.service-section a {
    color: #34ebd5;
    text-decoration: none;
}

.service-section a:hover {
    text-decoration: underline;
}

.contact-info {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.contact-info p {
    margin: 5px 0;
}

/* Guide Modal Layout Overrides */
/* Override: User Guide Modal Grid Layout */
#guideModal .guide-modal {
    width: 75vw !important;
    max-width: 1400px !important;
    height: auto !important;
}

#guideModal .guide-modal .guide-body {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    grid-template-rows: auto auto !important;
    gap: 5px !important;
    padding: 5px !important;
}

#guideModal .guide-modal .guide-body .guide-content {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 5px !important;
    grid-column: 1 / -1 !important;
}

#guideModal .guide-modal .guide-body .guide-actions {
    grid-column: 1 / -1 !important;
    justify-self: center !important;
}

/* INTRODUCTION PAGE LAYOUT OVERRIDE */
.guide-modal .guide-body .guide-content {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    grid-template-rows: repeat(3, auto) auto !important;
    gap: 5px !important;
}

.guide-modal .guide-body .guide-content > .guide-actions {
    grid-column: 1 / -1 !important;
}

/* INTRODUCTION PAGE CUSTOM OVERRIDES */
.guide-modal {
    width: 75vw !important;
    height: auto !important;
    max-width: 1400px !important;
    max-height: 95vh !important;
    display: grid !important;
    grid-template-columns: 1fr !important;
    grid-template-rows: auto 1fr auto !important;
}

.guide-modal > .guide-header,
.guide-modal > .guide-body,
.guide-modal > .guide-actions {
    grid-column: 1 / -1 !important;
}

.guide-modal .guide-body {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    grid-template-rows: auto auto auto !important;
    gap: 5px !important;
    padding: 5px !important;
}

.guide-modal .guide-body .guide-content {
    display: contents !important;
}

.guide-modal .guide-body .guide-actions {
    grid-column: 1 / -1 !important;
    grid-row: 3 !important;
    justify-self: center !important;
}
