/**
 * Configuration settings for the KMS Rental Grid application
 */

// Grid dimensions configuration
export const gridConfig = {
    defaultRows: 20,
    defaultCols: 36
};

// Content mode configuration (rows, cols)
export const contentModes = {
    // All modes are uniformly set to 36x20
    "1": { rows: 20, cols: 36 },
    "2": { rows: 20, cols: 36 },
    "3": { rows: 20, cols: 36 },
    "4": { rows: 20, cols: 36 },
    "5": { rows: 20, cols: 36 },
    "6": { rows: 20, cols: 36 },
    "7": { rows: 20, cols: 36 },
    "8": { rows: 20, cols: 36 },
    "9": { rows: 20, cols: 36 },
    "10": { rows: 20, cols: 36 },
    "11": { rows: 20, cols: 36 },
    "12": { rows: 20, cols: 36 },
    "13": { rows: 20, cols: 36 },
    "14": { rows: 20, cols: 36 },
    "15": { rows: 20, cols: 36 },
    "16": { rows: 20, cols: 36 },
    "17": { rows: 20, cols: 36 },
    "18": { rows: 20, cols: 36 },
    "19": { rows: 20, cols: 36 },
    "20": { rows: 20, cols: 36 },
    "21": { rows: 20, cols: 36 },
    "22": { rows: 20, cols: 36 },
    "23": { rows: 20, cols: 36 },
    "24": { rows: 20, cols: 36 },
    "25": { rows: 20, cols: 36 },
    "26": { rows: 20, cols: 36 },
    "27": { rows: 20, cols: 36 },
    "28": { rows: 20, cols: 36 },
    "29": { rows: 20, cols: 36 },
    "30": { rows: 20, cols: 36 },
    "31": { rows: 20, cols: 36 },
    "32": { rows: 20, cols: 36 },
    "33": { rows: 20, cols: 36 },
    "34": { rows: 20, cols: 36 },
    "35": { rows: 20, cols: 36 },
    "36": { rows: 20, cols: 36 },
    "37": { rows: 20, cols: 36 },
    "38": { rows: 20, cols: 36 },
    "39": { rows: 20, cols: 36 },
    "40": { rows: 20, cols: 36 }
};

// Default application settings
export const defaultSettings = {
    defaultCategory: 'A',
    defaultGridId: '1',
    defaultInnerGridId: '1'
};

// Service content configuration
export const serviceContent = {
    'customer-service': {
        title: 'Customer Service',
        content: `
            <div class="service-content">
                <h3>Welcome to KempireS Customer Service Center</h3>
                <div class="contact-info">
                    <p><strong>Customer Service Hotline:</strong> (02) 1234-5678</p>
                    <p><strong>Service Hours:</strong> Monday to Friday 09:00-18:00</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                </div>

                <div class="service-section">
                    <h4>Frequently Asked Questions</h4>
                    <ul>
                        <li><a href="#faq1">How do I upgrade my membership level?</a></li>
                        <li><a href="#faq2">How can I change my account information?</a></li>
                        <li><a href="#faq3">How do I report inappropriate content?</a></li>
                    </ul>
                </div>

                <div class="service-section">
                    <h4>Online Support</h4>
                    <p>Click the button below to start chatting with our customer service</p>
                    <button class="service-chat-btn">Start Chat</button>
                </div>
            </div>
        `
    },
    'terms': {
        title: 'Terms of Use',
        content: `
            <div class="service-content">
                <h3>KempireS Terms of Use</h3>
                <div class="service-section">
                    <p>Last updated: December 1, 2023</p>
                    <p>Welcome to the KempireS virtual real estate platform. Please read the following terms carefully; by using this service, you agree to these terms.</p>

                    <h4>1. Service Description</h4>
                    <p>KempireS provides virtual real estate transactions, social interactions, and content sharing services. We reserve the right to modify the service content at any time.</p>

                    <h4>2. User Responsibilities</h4>
                    <p>You are responsible for your account activities and must comply with all applicable laws and regulations. Using this platform for any illegal activities is prohibited.</p>

                    <h4>3. Content Policy</h4>
                    <p>User-created content must comply with our community guidelines. We reserve the right to remove content that violates these guidelines.</p>

                    <h4>4. Virtual Assets</h4>
                    <p>Virtual real estate and items on the platform are only valid within the KempireS platform and have no actual monetary value.</p>
                </div>
            </div>
        `
    },
    'privacy': {
        title: 'Privacy Policy',
        content: `
            <div class="service-content">
                <h3>KempireS Privacy Policy</h3>
                <div class="service-section">
                    <p>Last updated: December 1, 2023</p>
                    <p>KempireS values your privacy. This policy explains how we collect, use, and protect your personal data.</p>

                    <h4>1. Data Collection</h4>
                    <p>We collect registration data you provide, data generated when using our services, and device information.</p>

                    <h4>2. Data Usage</h4>
                    <p>We use the collected data to provide, maintain, and improve our services, as well as to offer you personalized experiences.</p>

                    <h4>3. Data Sharing</h4>
                    <p>We will not share your personal data with third parties unless we have your consent or are required by law.</p>

                    <h4>4. Data Security</h4>
                    <p>We take reasonable measures to protect your data from unauthorized access or disclosure.</p>
                </div>
            </div>
        `
    },
    'feedback': {
        title: 'User Experience Feedback',
        content: `
            <div class="service-content">
                <h3>We Value Your Opinion</h3>
                <p>Please share your experience and suggestions using KempireS to help us improve our services.</p>

                <form class="feedback-form" id="feedbackForm">
                    <div class="form-group">
                        <label for="feedbackType">Feedback Type</label>
                        <select id="feedbackType" class="form-control">
                            <option value="suggestion">Feature Suggestion</option>
                            <option value="bug">Bug Report</option>
                            <option value="compliment">Compliment</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="feedbackSubject">Subject</label>
                        <input type="text" id="feedbackSubject" class="form-control" placeholder="Briefly describe your feedback" required>
                    </div>

                    <div class="form-group">
                        <label for="feedbackContent">Detailed Content</label>
                        <textarea id="feedbackContent" class="form-control" rows="5" placeholder="Please describe your opinion or suggestion in detail" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="contactEmail">Contact Email (Optional)</label>
                        <input type="email" id="contactEmail" class="form-control" placeholder="If you need a reply, please provide your email">
                    </div>

                    <button type="submit" class="feedback-submit-btn">Submit Feedback</button>
                </form>

                <div id="feedbackSuccess" class="success-message" style="display: none;">
                    Thank you for your feedback! We have received your opinion and will consider it carefully.
                </div>
            </div>
        `
    }
};

// Premium content configuration
export const premiumContent = {
    'emperor': {
        title: 'Emperor Exclusive Real Estate',
        content: `
            <div class="premium-content">
                <h3>Emperor Exclusive Prestigious Real Estate</h3>
                <p class="premium-description">Own the most prestigious virtual real estate in KempireS and enjoy unparalleled privileges and services.</p>

                <div class="premium-features">
                    <h4>Prestigious Privileges</h4>
                    <ul>
                        <li>Exclusively designed luxury virtual villas that can accommodate unlimited visitors</li>
                        <li>Dedicated customer service team available 24 hours</li>
                        <li>Priority participation in all platform activities and testing of new features</li>
                        <li>Unlimited room creation and customization options</li>
                        <li>Exclusive badges and profile effects</li>
                        <li>Lifetime access to all premium features on the platform</li>
                    </ul>
                </div>

                <div class="premium-pricing">
                    <div class="premium-price">$999.99 / year</div>
                    <button class="premium-btn">Upgrade Now</button>
                </div>
            </div>
        `,
        imageSrc: 'Images/premium/emperor_estate.jpg'
    },
    'vip1': {
        title: 'Premium VIP Real Estate - Gold Package',
        content: `
            <div class="premium-content">
                <h3>Gold VIP Real Estate</h3>
                <p class="premium-description">Upgrade your virtual real estate experience and enjoy more privileges and features.</p>

                <div class="premium-features">
                    <h4>VIP Privileges</h4>
                    <ul>
                        <li>Premium virtual apartments that can accommodate up to 100 visitors</li>
                        <li>Priority customer support</li>
                        <li>More room customization options</li>
                        <li>VIP badge display</li>
                        <li>50% reduction in advertisements</li>
                    </ul>
                </div>

                <div class="premium-pricing">
                    <div class="premium-price">$99.99 / year</div>
                    <button class="premium-btn">Upgrade Now</button>
                </div>
            </div>
        `,
        imageSrc: 'Images/premium/vip1_estate.jpg'
    },
    'appstore': {
        title: 'KMS App Store',
        content: `
            <div class="app-store-content">
                <h3>KMS App Store</h3>
                <p class="app-store-description">Explore curated applications designed for KempireS users to enhance your virtual life experience.</p>

                <div class="app-categories">
                    <div class="app-category">
                        <h4>Popular Apps</h4>
                        <div class="app-list">
                            <div class="app-item">
                                <div class="app-icon">🏠</div>
                                <div class="app-info">
                                    <div class="app-name">Property Manager</div>
                                    <div class="app-rating">★★★★★</div>
                                </div>
                                <button class="app-download-btn">Download</button>
                            </div>
                            <div class="app-item">
                                <div class="app-icon">💬</div>
                                <div class="app-info">
                                    <div class="app-name">KMS Chat Room</div>
                                    <div class="app-rating">★★★★☆</div>
                                </div>
                                <button class="app-download-btn">Download</button>
                            </div>
                            <div class="app-item">
                                <div class="app-icon">📊</div>
                                <div class="app-info">
                                    <div class="app-name">Market Analysis</div>
                                    <div class="app-rating">★★★★☆</div>
                                </div>
                                <button class="app-download-btn">Download</button>
                            </div>
                        </div>
                    </div>

                    <div class="app-category">
                        <h4>Productivity Tools</h4>
                        <div class="app-list">
                            <div class="app-item">
                                <div class="app-icon">📅</div>
                                <div class="app-info">
                                    <div class="app-name">Event Calendar</div>
                                    <div class="app-rating">★★★★☆</div>
                                </div>
                                <button class="app-download-btn">Download</button>
                            </div>
                            <div class="app-item">
                                <div class="app-icon">📝</div>
                                <div class="app-info">
                                    <div class="app-name">Property Notes</div>
                                    <div class="app-rating">★★★★★</div>
                                </div>
                                <button class="app-download-btn">Download</button>
                            </div>
                            <div class="app-item">
                                <div class="app-icon">⏰</div>
                                <div class="app-info">
                                    <div class="app-name">Reminder Assistant</div>
                                    <div class="app-rating">★★★☆☆</div>
                                </div>
                                <button class="app-download-btn">Download</button>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="app-store-more-btn">View More Apps</button>
            </div>
        `,
        imageSrc: 'Images/app_store_banner.jpg'
    }
};
