<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服務按鈕測試頁面</title>
    <link rel="stylesheet" href="CSS/main.css">
    <style>
        body {
            background: #333;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        .service-buttons-test {
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 300px;
        }
        .test-info {
            color: #34ebd5;
            margin-bottom: 20px;
        }
        .debug-info {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            color: #fff;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: #34ebd5; text-align: center;">KempireS 服務按鈕測試</h1>
        
        <div class="test-section">
            <h3 style="color: #fff;">服務按鈕測試</h3>
            <div class="test-info">
                點擊下方按鈕測試服務功能是否正常運作
            </div>
            
            <div class="service-buttons-test">
                <div class="toggle-button service-button" data-service="customer-service">
                    <div class="toggle-button-label">Customer Service</div>
                    <div class="service-icon">📞</div>
                </div>
                <div class="toggle-button service-button" data-service="terms">
                    <div class="toggle-button-label">Terms of Service</div>
                    <div class="service-icon">📜</div>
                </div>
                <div class="toggle-button service-button" data-service="privacy">
                    <div class="toggle-button-label">Privacy Policy</div>
                    <div class="service-icon">🔒</div>
                </div>
                <div class="toggle-button service-button" data-service="feedback">
                    <div class="toggle-button-label">User Feedback</div>
                    <div class="service-icon">📝</div>
                </div>
            </div>
            
            <div class="debug-info" id="debugInfo">
                <strong>調試信息：</strong><br>
                等待按鈕點擊...
            </div>
        </div>
        
        <div class="test-section">
            <h3 style="color: #fff;">功能說明</h3>
            <ul style="color: #ccc;">
                <li><strong>Customer Service:</strong> 24/7 全球客服支援</li>
                <li><strong>Terms of Service:</strong> 完整的服務條款和用戶協議</li>
                <li><strong>Privacy Policy:</strong> GDPR/CCPA 合規的隱私政策</li>
                <li><strong>User Feedback:</strong> 專業的用戶反饋收集系統</li>
            </ul>
        </div>
    </div>

    <!-- Modal Structure -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="win-modal">
            <div class="titlebar">
                <span id="modalTitle">Service Information</span>
                <button class="close-btn" onclick="closeModal()">×</button>
            </div>
            <div class="modal-content" id="modalContent">
                <!-- Service content will be loaded here -->
            </div>
        </div>
    </div>

    <script type="module">
        // Import necessary modules
        import { getElement, getElements, addEvent } from './JS/modules/core/dom.js';
        import { serviceContent } from './JS/modules/core/config.js';
        import { showServiceModal, closeModal } from './JS/modules/ui/modal.js';

        // Make closeModal global for the close button
        window.closeModal = closeModal;

        // Debug function
        function updateDebugInfo(message) {
            const debugInfo = document.getElementById('debugInfo');
            if (debugInfo) {
                const timestamp = new Date().toLocaleTimeString();
                debugInfo.innerHTML += `<br>[${timestamp}] ${message}`;
                debugInfo.scrollTop = debugInfo.scrollHeight;
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('頁面載入完成');
            
            // Find and setup service buttons
            const serviceButtons = getElements('.service-button[data-service]');
            updateDebugInfo(`找到 ${serviceButtons.length} 個服務按鈕`);
            
            serviceButtons.forEach((button, index) => {
                const serviceType = button.dataset.service;
                updateDebugInfo(`設置按鈕 ${index + 1}: ${serviceType}`);
                
                addEvent(button, 'click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    updateDebugInfo(`按鈕被點擊: ${serviceType}`);
                    
                    try {
                        showServiceModal(serviceType, serviceContent);
                        updateDebugInfo(`模態框已顯示: ${serviceType}`);
                    } catch (error) {
                        updateDebugInfo(`錯誤: ${error.message}`);
                        console.error('Service modal error:', error);
                    }
                });
                
                // Add hover effect
                addEvent(button, 'mouseenter', function() {
                    updateDebugInfo(`滑鼠懸停: ${serviceType}`);
                });
            });
            
            updateDebugInfo('所有事件綁定完成');
        });

        // Test function for manual testing
        window.testServiceButton = function(serviceType) {
            updateDebugInfo(`手動測試: ${serviceType}`);
            try {
                showServiceModal(serviceType, serviceContent);
                updateDebugInfo(`手動測試成功: ${serviceType}`);
            } catch (error) {
                updateDebugInfo(`手動測試失敗: ${error.message}`);
            }
        };
    </script>

    <div style="position: fixed; bottom: 20px; right: 20px; background: rgba(0,0,0,0.8); padding: 15px; border-radius: 10px; color: #34ebd5;">
        <strong>手動測試：</strong><br>
        <button onclick="testServiceButton('customer-service')" style="margin: 2px; padding: 5px 10px; background: #34ebd5; color: #333; border: none; border-radius: 3px; cursor: pointer;">客服</button>
        <button onclick="testServiceButton('terms')" style="margin: 2px; padding: 5px 10px; background: #34ebd5; color: #333; border: none; border-radius: 3px; cursor: pointer;">條款</button>
        <button onclick="testServiceButton('privacy')" style="margin: 2px; padding: 5px 10px; background: #34ebd5; color: #333; border: none; border-radius: 3px; cursor: pointer;">隱私</button>
        <button onclick="testServiceButton('feedback')" style="margin: 2px; padding: 5px 10px; background: #34ebd5; color: #333; border: none; border-radius: 3px; cursor: pointer;">反饋</button>
    </div>
</body>
</html>
