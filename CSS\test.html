<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS 模組化測試頁面</title>
    <link rel="stylesheet" href="main.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            margin-top: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .test-item {
            padding: 10px;
            background: #f5f5f5;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #333;">KMS Rental Grid - CSS 模組化測試</h1>
        
        <!-- 基礎樣式測試 -->
        <div class="test-section">
            <h3>1. 基礎樣式測試 (base.css)</h3>
            <div class="search-container" style="position: relative; width: 300px;">
                <div class="search-bar">
                    <input type="text" placeholder="搜尋測試...">
                    <button><span class="search-icon">🔍</span></button>
                </div>
            </div>
            <div class="title-3d" style="position: relative; height: 100px; background: #333; margin-top: 10px;">
                KempireS
            </div>
        </div>

        <!-- 網格系統測試 -->
        <div class="test-section">
            <h3>2. 網格系統測試 (grid.css)</h3>
            <div class="test-grid">
                <div class="outer-grid-item test-item">外層網格項目</div>
                <div class="grid-item test-item">網格項目</div>
                <div class="online-status-container test-item">
                    <div class="location-text">位置: A1</div>
                    <div class="online-count">在線: <span class="count-number">5</span></div>
                </div>
            </div>
        </div>

        <!-- 按鈕樣式測試 -->
        <div class="test-section">
            <h3>3. 按鈕樣式測試 (buttons.css)</h3>
            <div class="test-grid">
                <button class="toggle-button test-item">
                    <div class="toggle-button-label">切換按鈕</div>
                </button>
                <button class="toggle-button-3d test-item">
                    <div class="toggle-button-label">3D 按鈕</div>
                    <div class="topic-icon">🎯</div>
                </button>
                <button class="premium-button test-item">
                    <div class="toggle-button-label">高級按鈕</div>
                    <div class="premium-icon">💎</div>
                </button>
                <button class="square-button test-item">方形按鈕</button>
            </div>
        </div>

        <!-- 主題顏色測試 -->
        <div class="test-section">
            <h3>4. 主題顏色測試 (themes.css)</h3>
            <div class="content-mode-1">
                <div class="test-grid">
                    <div class="grid-item test-item">模式 1</div>
                </div>
            </div>
            <div class="content-mode-25">
                <div class="test-grid">
                    <div class="grid-item test-item">模式 25</div>
                </div>
            </div>
        </div>

        <!-- 動畫效果測試 -->
        <div class="test-section">
            <h3>5. 動畫效果測試 (animations.css)</h3>
            <div class="test-grid">
                <div class="test-item pulse">脈衝動畫</div>
                <div class="test-item bounce">彈跳動畫</div>
                <div class="test-item fade-in">淡入動畫</div>
                <div class="test-item hover-lift">懸停提升</div>
            </div>
        </div>

        <!-- 響應式測試 -->
        <div class="test-section">
            <h3>6. 響應式設計測試 (responsive.css)</h3>
            <p>請調整瀏覽器視窗大小來測試響應式效果。</p>
            <div class="test-grid">
                <div class="test-item">響應式項目 1</div>
                <div class="test-item">響應式項目 2</div>
                <div class="test-item">響應式項目 3</div>
            </div>
        </div>

        <!-- 測試結果 -->
        <div class="test-section">
            <h3>7. 測試結果</h3>
            <div id="test-results">
                <p>✅ 如果您能看到上述所有樣式正確顯示，表示 CSS 模組化重構成功！</p>
                <p>📁 新的 CSS 文件結構：</p>
                <ul>
                    <li>base.css - 基礎樣式</li>
                    <li>grid.css - 網格系統</li>
                    <li>buttons.css - 按鈕樣式</li>
                    <li>modals.css - 模態框樣式</li>
                    <li>chat.css - 聊天系統</li>
                    <li>auth.css - 認證系統</li>
                    <li>themes.css - 主題顏色</li>
                    <li>animations.css - 動畫效果</li>
                    <li>responsive.css - 響應式設計</li>
                    <li>main.css - 主文件（引用所有模組）</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 簡單的測試腳本
        document.addEventListener('DOMContentLoaded', function() {
            console.log('CSS 模組化測試頁面載入完成');
            
            // 檢查 CSS 是否正確載入
            const testElement = document.querySelector('.toggle-button');
            if (testElement) {
                const styles = window.getComputedStyle(testElement);
                console.log('按鈕背景色:', styles.backgroundColor);
                console.log('按鈕邊框半徑:', styles.borderRadius);
            }
            
            // 添加互動測試
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('按鈕點擊測試:', this.textContent.trim());
                });
            });
        });
    </script>
</body>
</html>
