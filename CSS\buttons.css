/* ===========
   Buttons - 按鈕樣式
   =========== */

/* 基本切換按鈕 */
.toggle-button {
    width: 100%;
    height: 100%;
    background-color: #1462d1;
    color: #fff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.toggle-button-label {
    font-size: 16px;
    padding: 4px 0;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.6);
    width: 100%;
    z-index: 2;
}

.toggle-button:hover {
    background-color: #ffd700;
}

/* 選擇中的按鈕樣式 */
.toggle-button.active {
    background-color: #ffd700;
    color: #fffb00;
}

/* 3D 按鈕樣式 */
.toggle-button-3d {
    background: linear-gradient(145deg, #2196f3, #00bcd4);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1),
               0 1px 3px rgba(0, 0, 0, 0.2),
               inset 0 -2px 0 rgba(0, 0, 0, 0.2),
               inset 0 2px 0 rgba(255, 255, 255, 0.2);
    transform-style: preserve-3d;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.toggle-button-3d:hover {
    background: linear-gradient(145deg, #ff9800, #f44336);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15),
               0 3px 6px rgba(0, 0, 0, 0.2),
               inset 0 -2px 0 rgba(0, 0, 0, 0.2),
               inset 0 2px 0 rgba(255, 255, 255, 0.2);
}

.toggle-button-3d:active {
    transform: translateY(1px);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1),
               0 1px 2px rgba(0, 0, 0, 0.1),
               inset 0 -1px 0 rgba(0, 0, 0, 0.2),
               inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.toggle-button-3d .toggle-button-label {
    background-color: rgba(0, 0, 0, 0.3);
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.toggle-button-3d .topic-icon {
    font-size: 24px;
    margin-top: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 跑馬燈按鈕 */
.toggle-button.marquee {
    overflow: hidden;
    background-color: #1462d1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.marquee-content {
    white-space: nowrap;
    display: inline-block;
    animation: marquee 15s linear infinite;
    animation-delay: 0s;
    font-size: 1.2rem;
}

/* 方形按鈕 */
.square-button {
    background-color: #061869;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    color: white;
    user-select: none;
    transition: all 0.2s ease;
}

.square-button:hover {
    background-color: #34ebd5;
    color: #08aba5;
    transform: scale(1.05);
}

/* 小型方形按鈕 (用於16格網格) */
.square-button-small {
    position: relative !important;
    z-index: 9999 !important; /* 最高堆疊順序 */
    background-color: #00bcff;
    border: none;
    border-radius: 3px;
    cursor: pointer !important; /* 確保滑鼠事件 */
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    color: white;
    user-select: none;
    transition: background-color 0.2s ease;
    pointer-events: auto !important; /* 確保滑鼠事件 */
}

.square-button-small:hover {
    background-color: #fffb00;
}

/* 小按鈕選中狀態樣式 */
.square-button-small.active {
    background-color: #00ffe1 !important;
    color: #333 !important;
    box-shadow: 0 0 4px rgba(52, 235, 213, 0.6) !important;
}

/* 熱門話題樣式 */
.topic-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #444, #222);
}

.topic-button:hover {
    background: linear-gradient(135deg, #333, #111);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.topic-icon {
    font-size: 24px;
    margin: 5px 0;
}

/* 高級房地產按鈕樣式 */
.premium-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #3a1c71, #d76d77, #ffaf7b);
    overflow: hidden;
    position: relative;
}

.premium-button::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 45%,
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0.1) 55%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

.premium-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.premium-icon {
    font-size: 24px;
    margin: 5px 0;
    z-index: 2;
}

/* 凱帝王專屬樣式 */
.premium-button[data-premium="emperor"] {
    background: linear-gradient(135deg, #b78628, #fcc201, #b78628);
}

.premium-button[data-premium="emperor"] .premium-icon {
    font-size: 32px;
    animation: pulse 2s infinite;
}

/* App Store 按鈕樣式 */
.app-store-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #2193b0, #6dd5ed);
}

.app-store-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #1c839f, #5bc2db);
}

.app-store-icon {
    font-size: 28px;
    margin: 5px 0;
}

/* 升級房子按鈕 */
.house-upgrade-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.house-upgrade-btn:hover {
    background-color: rgba(52, 235, 213, 0.2);
}

.upgrade-icon {
    font-size: 28px;
    margin-bottom: 5px;
}

.upgrade-text {
    font-size: 16px;
    font-weight: bold;
    color: #34ebd5;
}

/* 個人資料編輯按鈕 */
.profile-edit-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.profile-edit-btn:hover {
    background-color: rgba(52, 235, 213, 0.2);
}

.profile-icon {
    font-size: 32px;
    margin-bottom: 5px;
}

.profile-text {
    font-size: 16px;
    font-weight: bold;
    color: #34ebd5;
}

/* 房子架構編輯按鈕 */
.house-struct-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.house-struct-btn:hover {
    background-color: rgba(52, 235, 213, 0.2);
}

.struct-icon {
    font-size: 32px;
    margin-bottom: 5px;
}

.struct-text {
    font-size: 16px;
    font-weight: bold;
    color: #34ebd5;
}

/* 服務按鈕 */
.service-button {
    display: flex;
    flex: 1;
    width: 100%;
    height: auto;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 5px;
    transition: all 0.3s ease;
    cursor: pointer;
    pointer-events: auto;
    position: relative;
    z-index: 10;
}

.service-button:hover {
    background-color: #34ebd5;
    color: #333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 235, 213, 0.3);
}

.service-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(52, 235, 213, 0.2);
}

.service-icon {
    font-size: 22px;
    margin-top: 5px;
    pointer-events: none;
}

/* 登入註冊按鈕 */
.login-register-btns {
    display: inline-grid;
    gap: 4px;
    width: -webkit-fill-available;
    height: -webkit-fill-available;
    margin: 6px;
}

.login-btn, .register-btn {
    flex: 1;
    padding: 8px 5px;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
}

.login-btn {
    background-color: #0078d4;
    color: white;
}

.register-btn {
    background-color: #34ebd5;
    color: #333;
}

.login-btn:hover, .register-btn:hover {
    filter: brightness(1.1);
}
