/**
 * Main entry point for the KMS Rental Grid application
 */

import { initializeApp } from './app.js';

// Initialize the application when the DOM is fully loaded
document.addEventListener("DOMContentLoaded", () => {
    console.log("DOM fully loaded, initializing application...");
    initializeApp();
});

 // Setup bottom row layout
 document.addEventListener('DOMContentLoaded', () => {
     // Remove old bottom-row items except Live Chat
     document.querySelectorAll('.outer-grid-item.column[style*="grid-row: 12"]').forEach(el => {
         if (el.getAttribute('style').includes('grid-column: 20;')) return;
         el.remove();
     });
     const gridContainer = document.querySelector('.grid-container');
     // Create service block at col 1
     const serviceBlock = document.createElement('div');
     serviceBlock.className = 'outer-grid-item column';
     serviceBlock.style.cssText = 'grid-column: 1; grid-row: 12; display: flex; flex-direction: column; gap: 8px;';
     ['Customer Service','Terms of Service','Privacy Policy','Uber Feedback'].forEach(label => {
         const btn = document.createElement('div');
         btn.className = 'toggle-button service-button';
         btn.innerHTML = `<div class="toggle-button-label">${label}</div>`;
         serviceBlock.appendChild(btn);
     });
     gridContainer.appendChild(serviceBlock);
     // Create Premium VIP cells for T2-T8 (cols 2-8)
     [2,3,4,5,6,7,8].forEach(col => {
         const vipItem = document.createElement('div');
         vipItem.className = 'outer-grid-item column';
         vipItem.style.cssText = `grid-column: ${col}; grid-row: 12;`;
         vipItem.innerHTML = `
             <div class="toggle-button premium-button" data-premium="vip1">
                 <div class="toggle-button-label">Premium VIP Properties</div>
                 <div class="premium-icon">💎</div>
             </div>
         `;
         gridContainer.appendChild(vipItem);
     });
     // Create marquee block spanning cols 9-12
     const marqueeItem = document.createElement('div');
     marqueeItem.className = 'outer-grid-item column';
     marqueeItem.style.cssText = 'grid-column: 9 / span 4; grid-row: 12;';
     marqueeItem.innerHTML = `
         <div class="toggle-button marquee">
             <div class="marquee-content">Kelvin at A-1-1 has new event. <a href="#">Click here for detail.</a></div>
         </div>
     `;
     gridContainer.appendChild(marqueeItem);
     // Create Premium VIP cells for T13-T19 (cols 13-19)
     [13,14,15,16,17,18,19].forEach(col => {
         const vipItem = document.createElement('div');
         vipItem.className = 'outer-grid-item column';
         vipItem.style.cssText = `grid-column: ${col}; grid-row: 12;`;
         vipItem.innerHTML = `
             <div class="toggle-button premium-button" data-premium="vip1">
                 <div class="toggle-button-label">Premium VIP Properties</div>
                 <div class="premium-icon">💎</div>
             </div>
         `;
         gridContainer.appendChild(vipItem);
     });
 });
