/**
 * Guide modal functionality for the KMS Rental Grid application
 */

import { getElement, showElement, hideElement } from '../core/dom.js';

// Cache guide modal elements
let guideModal, guideOverlay;
let isGuideInitialized = false;

/**
 * Initialize guide modal elements and event listeners
 */
export function initializeGuide() {
    if (isGuideInitialized) return;

    guideOverlay = getElement('guideModal');
    guideModal = document.querySelector('.guide-modal');

    if (!guideOverlay) {
        console.error('Guide modal overlay not found');
        return;
    }

    // Add event listeners
    setupGuideEventListeners();
    
    // Add help button event listener
    const helpButton = getElement('helpButton');
    if (helpButton) {
        helpButton.addEventListener('click', showGuide);
    }
    
    isGuideInitialized = true;
    console.log('Guide modal initialized');
}

/**
 * Setup event listeners for guide modal
 */
function setupGuideEventListeners() {
    // Close button
    const closeBtn = getElement('guideCloseBtn');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeGuide);
    }

    // Login button
    const loginBtn = getElement('guideLoginBtn');
    if (loginBtn) {
        loginBtn.addEventListener('click', () => {
            closeGuide();
            // Trigger login modal
            const loginModalBtn = getElement('loginBtn');
            if (loginModalBtn) {
                loginModalBtn.click();
            }
        });
    }

    // Register button
    const registerBtn = getElement('guideRegisterBtn');
    if (registerBtn) {
        registerBtn.addEventListener('click', () => {
            closeGuide();
            // Trigger register modal
            const registerModalBtn = getElement('registerBtn');
            if (registerModalBtn) {
                registerModalBtn.click();
            }
        });
    }

    // Explore button
    const exploreBtn = getElement('guideExploreBtn');
    if (exploreBtn) {
        exploreBtn.addEventListener('click', () => {
            closeGuide();
            // Just close the guide and let user explore
            console.log('User chose to explore without logging in');
        });
    }

    // Close when clicking outside the modal
    if (guideOverlay) {
        guideOverlay.addEventListener('click', (e) => {
            if (e.target === guideOverlay) {
                closeGuide();
            }
        });
    }

    // Close with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && isGuideVisible()) {
            closeGuide();
        }
    });
}

/**
 * Show the guide modal
 */
export function showGuide() {
    if (!isGuideInitialized) {
        initializeGuide();
    }

    if (guideOverlay) {
        showElement(guideOverlay, 'flex');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
        
        // Add animation class
        if (guideModal) {
            guideModal.style.animation = 'guideModalSlideIn 0.5s ease-out';
        }
        
        console.log('Guide modal shown');
    }
}

/**
 * Close the guide modal
 */
export function closeGuide() {
    if (guideOverlay) {
        // Add fade out animation
        if (guideModal) {
            guideModal.style.animation = 'guideModalSlideOut 0.3s ease-in';
            
            setTimeout(() => {
                hideElement(guideOverlay);
                document.body.style.overflow = ''; // Restore scrolling
                
                // Reset animation
                if (guideModal) {
                    guideModal.style.animation = '';
                }
            }, 300);
        } else {
            hideElement(guideOverlay);
            document.body.style.overflow = '';
        }
        
        console.log('Guide modal closed');
    }
}

/**
 * Check if guide modal is currently visible
 * @returns {boolean}
 */
export function isGuideVisible() {
    return guideOverlay && guideOverlay.style.display === 'flex';
}

/**
 * Show guide on first visit (if user is not logged in)
 */
export function showGuideOnFirstVisit() {
    // Check if user has seen the guide before
    const hasSeenGuide = localStorage.getItem('kms_guide_seen');
    const isLoggedIn = localStorage.getItem('kms_user_logged_in'); // Assuming this exists
    
    if (!hasSeenGuide && !isLoggedIn) {
        // Show guide after a short delay
        setTimeout(() => {
            showGuide();
            // Mark as seen
            localStorage.setItem('kms_guide_seen', 'true');
        }, 1000);
    }
}

/**
 * Reset guide seen status (for testing purposes)
 */
export function resetGuideStatus() {
    localStorage.removeItem('kms_guide_seen');
    console.log('Guide status reset');
}