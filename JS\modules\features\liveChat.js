/**
 * Live Chat functionality for the KMS Rental Grid application
 */

import { getElement, addEvent, showElement, hideElement } from '../core/dom.js';

// Cache chat elements
let liveChatButton, liveChatWindow, chatCloseBtn, chatInput, chatSendBtn, chatMessages;
let waitingIndicator, estimatedWaitTime, queuePosition;

// Chat state
let isChatOpen = false;
let isWaiting = false;
let waitTimeInterval = null;
let currentWaitTime = 0;
let currentQueuePosition = 0;

/**
 * Initialize Live Chat functionality
 */
export function initializeLiveChat() {
    // Get chat elements
    liveChatButton = getElement('liveChatButton');
    liveChatWindow = getElement('liveChatWindow');
    chatCloseBtn = getElement('chatCloseBtn');
    chatInput = getElement('chatInput');
    chatSendBtn = getElement('chatSendBtn');
    chatMessages = getElement('chatMessages');
    waitingIndicator = getElement('waitingIndicator');
    estimatedWaitTime = getElement('estimatedWaitTime');
    queuePosition = getElement('queuePosition');
    
    // Initialize event listeners
    initializeChatButtons();
    initializeChatInput();
    updateClock();
    
    // Start clock update interval
    setInterval(updateClock, 60000); // Update every minute
}

/**
 * Initialize chat buttons
 */
function initializeChatButtons() {
    // Live Chat button
    if (liveChatButton) {
        addEvent(liveChatButton, 'click', function() {
            toggleChat();
        });
    }
    
    // Close button
    if (chatCloseBtn) {
        addEvent(chatCloseBtn, 'click', function() {
            closeChat();
        });
    }
}

/**
 * Initialize chat input
 */
function initializeChatInput() {
    if (chatInput && chatSendBtn) {
        // Send message on button click
        addEvent(chatSendBtn, 'click', function() {
            sendMessage();
        });
        
        // Send message on Enter key
        addEvent(chatInput, 'keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    }
}

/**
 * Toggle chat window
 */
function toggleChat() {
    if (isChatOpen) {
        closeChat();
    } else {
        openChat();
    }
}

/**
 * Open chat window
 */
function openChat() {
    if (liveChatWindow) {
        showElement(liveChatWindow, 'block');
        isChatOpen = true;
        
        // Simulate waiting for an agent
        simulateWaiting();
    }
}

/**
 * Close chat window
 */
function closeChat() {
    if (liveChatWindow) {
        hideElement(liveChatWindow);
        isChatOpen = false;
        
        // Clear waiting simulation
        clearWaitingSimulation();
    }
}

/**
 * Send a message
 */
function sendMessage() {
    if (!chatInput || !chatMessages) return;
    
    const message = chatInput.value.trim();
    if (!message) return;
    
    // Add message to chat
    addMessageToChat(message, 'sent');
    
    // Clear input
    chatInput.value = '';
    
    // If waiting, show waiting indicator
    if (isWaiting) {
        showElement(waitingIndicator, 'block');
    } else {
        // Simulate agent response after a delay
        setTimeout(() => {
            simulateAgentResponse(message);
        }, 1000);
    }
}

/**
 * Add a message to the chat
 * @param {string} message - The message text
 * @param {string} type - The message type ('sent' or 'received')
 */
function addMessageToChat(message, type) {
    if (!chatMessages) return;
    
    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'sent' ? 'message-sent' : 'message-received';
    
    // Create message content
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = `<p>${message}</p>`;
    
    // Create message time
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    
    // Get current time
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    timeDiv.textContent = `${hours}:${minutes}`;
    
    // Append elements
    messageDiv.appendChild(contentDiv);
    messageDiv.appendChild(timeDiv);
    chatMessages.appendChild(messageDiv);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

/**
 * Simulate waiting for an agent
 */
function simulateWaiting() {
    if (!waitingIndicator || !estimatedWaitTime || !queuePosition) return;
    
    // Set waiting state
    isWaiting = true;
    
    // Set initial wait time and queue position
    currentWaitTime = 2; // 2 minutes
    currentQueuePosition = 3;
    
    // Update UI
    estimatedWaitTime.textContent = currentWaitTime;
    queuePosition.textContent = currentQueuePosition;
    
    // Show waiting indicator
    showElement(waitingIndicator, 'block');
    
    // Start wait time countdown
    waitTimeInterval = setInterval(() => {
        updateWaitTime();
    }, 10000); // Update every 10 seconds
    
    // Simulate connecting to agent after wait time
    setTimeout(() => {
        connectToAgent();
    }, 30000); // Connect after 30 seconds (for demo)
}

/**
 * Update wait time
 */
function updateWaitTime() {
    if (!estimatedWaitTime || !queuePosition) return;
    
    // Decrease wait time
    if (currentWaitTime > 0) {
        currentWaitTime -= 0.5;
        estimatedWaitTime.textContent = currentWaitTime.toFixed(1);
    }
    
    // Decrease queue position
    if (currentQueuePosition > 1) {
        currentQueuePosition -= 1;
        queuePosition.textContent = currentQueuePosition;
    }
}

/**
 * Clear waiting simulation
 */
function clearWaitingSimulation() {
    isWaiting = false;
    
    if (waitTimeInterval) {
        clearInterval(waitTimeInterval);
        waitTimeInterval = null;
    }
    
    if (waitingIndicator) {
        hideElement(waitingIndicator);
    }
}

/**
 * Connect to agent
 */
function connectToAgent() {
    // Clear waiting simulation
    clearWaitingSimulation();
    
    // Add system message
    const systemDiv = document.createElement('div');
    systemDiv.className = 'message-system';
    systemDiv.innerHTML = '<span>您已連接到客服人員</span>';
    chatMessages.appendChild(systemDiv);
    
    // Add agent message
    setTimeout(() => {
        addMessageToChat('您好！我是客服人員小美，很高興為您服務。請問有什麼可以幫助您的嗎？', 'received');
    }, 1000);
}

/**
 * Simulate agent response
 * @param {string} userMessage - The user's message
 */
function simulateAgentResponse(userMessage) {
    // Simple response logic based on user message
    let response = '';
    
    if (userMessage.includes('real estate') || userMessage.includes('house')) {
        response = 'For questions about real estate, you can check the "Real Estate Management" section for related options, or let me know specifically what information you\'re looking for.';
    } else if (userMessage.includes('price') || userMessage.includes('cost') || userMessage.includes('how much')) {
        response = 'Our prices vary depending on the type and location of the real estate. Basic prices start from 100 virtual coins, and VIP areas are more expensive. You can view detailed pricing in the price list.';
    } else if (userMessage.includes('upgrade') || userMessage.includes('enhance')) {
        response = 'To upgrade your real estate, click the "Upgrade House Size" button on the right side and follow the instructions. Upgrading requires a certain amount of virtual currency.';
    } else if (userMessage.includes('thank you') || userMessage.includes('thanks')) {
        response = 'You\'re welcome! I\'m glad I could help. If you have any other questions, feel free to ask me.';
    } else {
        response = 'Thank you for your question. Could you provide more details? This will help me better understand and assist you.';
    }

    // Add agent response
    addMessageToChat(response, 'received');
}

/**
 * Update clock in status bar
 */
function updateClock() {
    const timeElement = document.querySelector('.iphone-status-bar .time');
    if (!timeElement) return;
    
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    
    timeElement.textContent = `${hours}:${minutes}`;
}
