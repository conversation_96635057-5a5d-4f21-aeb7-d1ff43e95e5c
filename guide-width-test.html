<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>介紹頁寬度測試</title>
    <link rel="stylesheet" href="CSS/main.css">
    <style>
        body {
            background: #333;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            color: #fff;
        }
        .test-button {
            background: linear-gradient(135deg, #34ebd5, #00a8cc);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 18px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 235, 213, 0.4);
        }
        .info-box {
            background: rgba(52, 235, 213, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #34ebd5;
        }
        .width-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #34ebd5;
            padding: 10px 15px;
            border-radius: 8px;
            font-family: monospace;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div class="width-indicator" id="widthIndicator">
        視窗寬度: <span id="windowWidth"></span>px
    </div>

    <div class="test-container">
        <h1 style="text-align: center; color: #34ebd5;">📐 介紹頁寬度測試</h1>
        
        <div class="info-box">
            <h3>🎯 測試目標</h3>
            <p>驗證介紹頁（User Guide Modal）的寬度是否正確設置為畫面的 60%</p>
            <ul>
                <li><strong>桌面設備:</strong> 60% 視窗寬度，最大 1200px</li>
                <li><strong>平板設備:</strong> 75% 視窗寬度，最大 900px</li>
                <li><strong>手機設備:</strong> 90% 視窗寬度</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="test-button" onclick="openGuideModal()">
                🚀 打開介紹頁測試
            </button>
            <button class="test-button" onclick="showWidthInfo()">
                📊 顯示寬度信息
            </button>
        </div>

        <div class="info-box">
            <h3>📋 測試步驟</h3>
            <ol>
                <li>點擊「打開介紹頁測試」按鈕</li>
                <li>觀察介紹頁的寬度是否佔畫面的 60%</li>
                <li>調整瀏覽器視窗大小測試響應式效果</li>
                <li>在不同設備上測試（桌面、平板、手機）</li>
            </ol>
        </div>

        <div class="info-box">
            <h3>✅ 預期結果</h3>
            <ul>
                <li>介紹頁應該居中顯示</li>
                <li>左右兩側應該有適當的空白</li>
                <li>內容應該清晰可讀，不會過於擁擠</li>
                <li>在小屏幕上應該自動調整為更大的百分比</li>
            </ul>
        </div>

        <div id="testResults" style="margin-top: 30px; padding: 20px; background: rgba(0,0,0,0.2); border-radius: 10px; display: none;">
            <h3>📊 測試結果</h3>
            <div id="resultsContent"></div>
        </div>
    </div>

    <!-- User Guide Modal -->
    <div class="guide-modal-overlay" id="guideModal" style="display: none;">
        <div class="guide-modal">
            <div class="guide-header">
                <span>🎉 歡迎來到 KempireS</span>
                <button class="guide-close-btn" onclick="closeGuideModal()">&times;</button>
            </div>
            <div class="guide-body">
                <div class="guide-content">
                    <div class="guide-section">
                        <div class="guide-icon">🏠</div>
                        <h3>虛擬房地產</h3>
                        <p>探索無限可能的虛擬房地產世界，買賣、租賃、投資，打造您的數位帝國。</p>
                    </div>
                    
                    <div class="guide-section">
                        <div class="guide-icon">👥</div>
                        <h3>社群互動</h3>
                        <p>與全球用戶互動，建立社群，分享經驗，一起在虛擬世界中成長。</p>
                    </div>
                    
                    <div class="guide-section">
                        <div class="guide-icon">💎</div>
                        <h3>高級功能</h3>
                        <p>解鎖高級會員專屬功能，享受更多特權和優質服務體驗。</p>
                    </div>
                    
                    <div class="guide-section">
                        <div class="guide-icon">📱</div>
                        <h3>行動應用</h3>
                        <p>隨時隨地管理您的虛擬資產，手機 App 讓您不錯過任何機會。</p>
                    </div>
                    
                    <div class="guide-section">
                        <div class="guide-icon">🔒</div>
                        <h3>安全保障</h3>
                        <p>企業級安全措施保護您的資產和隱私，讓您安心享受服務。</p>
                    </div>
                    
                    <div class="guide-section">
                        <div class="guide-icon">🎯</div>
                        <h3>個性化體驗</h3>
                        <p>AI 智能推薦，為您量身打造專屬的虛擬世界體驗。</p>
                    </div>
                </div>
                
                <div class="guide-actions">
                    <button class="guide-btn guide-btn-primary" onclick="closeGuideModal()">
                        🚀 開始探索
                    </button>
                    <button class="guide-btn guide-btn-secondary" onclick="measureModalWidth()">
                        📐 測量寬度
                    </button>
                    <button class="guide-btn guide-btn-explore" onclick="closeGuideModal()">
                        ⏭️ 稍後再看
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新視窗寬度顯示
        function updateWindowWidth() {
            const width = window.innerWidth;
            document.getElementById('windowWidth').textContent = width;
        }

        // 打開介紹頁
        function openGuideModal() {
            document.getElementById('guideModal').style.display = 'flex';
            setTimeout(measureModalWidth, 100); // 等待動畫完成後測量
        }

        // 關閉介紹頁
        function closeGuideModal() {
            document.getElementById('guideModal').style.display = 'none';
        }

        // 測量模態框寬度
        function measureModalWidth() {
            const modal = document.querySelector('.guide-modal');
            const windowWidth = window.innerWidth;
            const modalWidth = modal.offsetWidth;
            const percentage = ((modalWidth / windowWidth) * 100).toFixed(1);
            
            const results = document.getElementById('testResults');
            const content = document.getElementById('resultsContent');
            
            content.innerHTML = `
                <p><strong>視窗寬度:</strong> ${windowWidth}px</p>
                <p><strong>模態框寬度:</strong> ${modalWidth}px</p>
                <p><strong>佔比:</strong> ${percentage}%</p>
                <p><strong>目標:</strong> 60%</p>
                <p><strong>狀態:</strong> ${Math.abs(percentage - 60) < 5 ? '✅ 符合預期' : '❌ 需要調整'}</p>
            `;
            
            results.style.display = 'block';
            
            // 在控制台輸出詳細信息
            console.log('Modal Width Test Results:', {
                windowWidth,
                modalWidth,
                percentage,
                target: 60,
                status: Math.abs(percentage - 60) < 5 ? 'PASS' : 'FAIL'
            });
        }

        // 顯示寬度信息
        function showWidthInfo() {
            const width = window.innerWidth;
            let deviceType = '';
            let expectedWidth = '';
            
            if (width > 1024) {
                deviceType = '桌面設備';
                expectedWidth = '60% (最大 1200px)';
            } else if (width > 768) {
                deviceType = '平板設備';
                expectedWidth = '75% (最大 900px)';
            } else {
                deviceType = '手機設備';
                expectedWidth = '90%';
            }
            
            alert(`設備類型: ${deviceType}\n視窗寬度: ${width}px\n預期模態框寬度: ${expectedWidth}`);
        }

        // 監聽視窗大小變化
        window.addEventListener('resize', updateWindowWidth);
        
        // 初始化
        updateWindowWidth();
        
        // ESC 鍵關閉模態框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeGuideModal();
            }
        });
    </script>
</body>
</html>
