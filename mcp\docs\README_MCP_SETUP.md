# PayPal Agent Toolkit MCP Server Setup

This directory contains the setup files for integrating the PayPal Agent Toolkit MCP server with your project.

## Quick Start

### Option 1: Windows Batch Script
```bash
setup-paypal-mcp.bat
```

### Option 2: PowerShell Script (Recommended for Windows)
```powershell
.\setup-paypal-mcp.ps1
```

### Option 3: Manual Setup
Follow the detailed instructions in [`PAYPAL_MCP_SETUP.md`](PAYPAL_MCP_SETUP.md:1)

## Files Overview

| File | Description |
|------|-------------|
| [`mcp_settings.json`](mcp_settings.json:1) | MCP server configuration file |
| [`setup-paypal-mcp.bat`](setup-paypal-mcp.bat:1) | Windows batch setup script |
| [`setup-paypal-mcp.ps1`](setup-paypal-mcp.ps1:1) | PowerShell setup script |
| [`test-paypal-mcp.js`](test-paypal-mcp.js:1) | Node.js test script to verify setup |
| [`PAYPAL_MCP_SETUP.md`](PAYPAL_MCP_SETUP.md:1) | Comprehensive setup documentation |

## Prerequisites

- **Node.js 18+** (Download from [nodejs.org](https://nodejs.org/))
- **PayPal Developer Account** (Sign up at [developer.paypal.com](https://developer.paypal.com/))

## Setup Process

1. **Install Node.js** (if not already installed)
2. **Run setup script** to install PayPal MCP package
3. **Get PayPal credentials** from Developer Dashboard
4. **Generate access token** using provided scripts
5. **Update configuration** in [`mcp_settings.json`](mcp_settings.json:8)
6. **Test setup** using [`test-paypal-mcp.js`](test-paypal-mcp.js:1)

## Configuration

The MCP server is configured with the name `github.com/paypal/agent-toolkit` as specified in [`mcp_settings.json`](mcp_settings.json:3).

### Environment Variables

- `PAYPAL_ACCESS_TOKEN`: Your PayPal API access token
- `PAYPAL_ENVIRONMENT`: Set to `SANDBOX` for testing or `PRODUCTION` for live

## Available Tools

The PayPal MCP server provides 20+ tools for:

- 📧 **Invoice Management**: Create, send, track invoices
- 💳 **Payment Processing**: Handle orders and payments  
- ⚖️ **Dispute Management**: Manage payment disputes
- 📦 **Shipment Tracking**: Track order shipments
- 🛍️ **Catalog Management**: Manage products and inventory
- 🔄 **Subscriptions**: Handle recurring payments
- 📊 **Reporting**: Access transaction data

## Testing

After setup, test the installation:

```bash
node test-paypal-mcp.js
```

Or test the MCP server directly:

```bash
npx -y @paypal/mcp --tools=all
```

## Integration

### With Claude Desktop
Add to `~/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "paypal": {
      "command": "npx",
      "args": ["-y", "@paypal/mcp", "--tools=all"],
      "env": {
        "PAYPAL_ACCESS_TOKEN": "YOUR_ACCESS_TOKEN",
        "PAYPAL_ENVIRONMENT": "SANDBOX"
      }
    }
  }
}
```

### With Other MCP Clients
Use the [`mcp_settings.json`](mcp_settings.json:1) configuration file.

## Support

- [PayPal Agent Toolkit GitHub](https://github.com/paypal/agent-toolkit)
- [PayPal Developer Documentation](https://developer.paypal.com/)
- [Model Context Protocol](https://modelcontextprotocol.com/)