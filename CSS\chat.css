/* ===========
   Live Chat System - 聊天系統樣式
   =========== */

/* Live Chat 按鈕樣式 */
.live-chat-button {
    background: linear-gradient(135deg, #00c6ff, #0072ff);
    transition: all 0.3s ease;
}

.live-chat-button:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #00d2ff, #0080ff);
    box-shadow: 0 8px 16px rgba(0, 114, 255, 0.3);
}

.live-chat-icon-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
}

.live-chat-icon {
    font-size: 32px;
    margin-bottom: 8px;
    animation: pulse 2s infinite;
}

.live-chat-status {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

/* Live Chat 視窗樣式 (iPhone 風格 - Dark Theme) */
.live-chat-window {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 525px;   /* Increased by 50% */
    height: 900px;  /* Increased by 50% */
    z-index: 3000;
    display: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    border-radius: 40px;
    overflow: hidden;
}

.iphone-container {
    width: 100%;
    height: 100%;
    background-color: #121212; /* Dark background */
    display: flex;
    flex-direction: column;
    position: relative;
    color: #e0e0e0; /* Light text color */
}

/* iPhone 頂部狀態欄 */
.iphone-status-bar {
    height: 66px; /* 44px * 1.5 */
    background-color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 22px; /* 15px * 1.5 */
    color: white;
    font-size: 21px; /* 14px * 1.5 */
}

.status-center {
    position: relative;
    height: 45px; /* 30px * 1.5 */
    width: 225px; /* 150px * 1.5 */
}

.notch {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 180px; /* 120px * 1.5 */
    height: 45px; /* 30px * 1.5 */
    background-color: #000;
    border-bottom-left-radius: 27px; /* 18px * 1.5 */
    border-bottom-right-radius: 27px; /* 18px * 1.5 */
}

.status-right {
    display: flex;
    align-items: center;
    gap: 7px; /* 5px * 1.5 */
}

/* 聊天頭部 */
.chat-header {
    height: 90px; /* 60px * 1.5 */
    background-color: #1a1a1a; /* Dark header */
    display: flex;
    align-items: center;
    padding: 0 22px; /* 15px * 1.5 */
    border-bottom: 1px solid #333;
}

.chat-back-btn {
    background: none;
    border: none;
    font-size: 30px; /* 20px * 1.5 */
    color: #34ebd5; /* Accent color */
    cursor: pointer;
    padding: 7px; /* 5px * 1.5 */
    margin-right: 15px; /* 10px * 1.5 */
}

.chat-profile {
    display: flex;
    align-items: center;
    flex: 1;
}

.chat-avatar {
    width: 60px; /* 40px * 1.5 */
    height: 60px; /* 40px * 1.5 */
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px; /* 10px * 1.5 */
    position: relative;
    border: 2px solid #34ebd5;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.online-indicator {
    position: absolute;
    bottom: 3px; /* 2px * 1.5 */
    right: 3px; /* 2px * 1.5 */
    width: 15px; /* 10px * 1.5 */
    height: 15px; /* 10px * 1.5 */
    background-color: #4cd964;
    border-radius: 50%;
    border: 3px solid #121212; /* 2px * 1.5 */
}

.chat-info {
    display: flex;
    flex-direction: column;
}

.chat-name {
    font-weight: 600;
    font-size: 24px; /* 16px * 1.5 */
    color: #ffffff;
}

.chat-status {
    font-size: 18px; /* 12px * 1.5 */
    color: #a0a0a0;
}

.chat-options-btn {
    background: none;
    border: none;
    font-size: 30px; /* 20px * 1.5 */
    color: #a0a0a0;
    cursor: pointer;
    padding: 7px; /* 5px * 1.5 */
}

/* 聊天內容區域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 22px; /* 15px * 1.5 */
    background-color: #0a0a0a; /* Dark background */
    display: flex;
    flex-direction: column;
    gap: 22px; /* 15px * 1.5 */
}

.chat-day-divider {
    text-align: center;
    margin: 15px 0; /* 10px * 1.5 */
    position: relative;
}

.chat-day-divider span {
    background-color: rgba(52, 235, 213, 0.2);
    color: #34ebd5;
    font-size: 18px; /* 12px * 1.5 */
    padding: 4px 15px; /* 3px*1.5, 10px*1.5 */
    border-radius: 15px; /* 10px * 1.5 */
}

.message-system {
    text-align: center;
    margin: 15px 0; /* 10px * 1.5 */
}

.message-system span {
    background-color: rgba(52, 235, 213, 0.2);
    color: #34ebd5;
    font-size: 18px; /* 12px * 1.5 */
    padding: 7px 15px; /* 5px*1.5, 10px*1.5 */
    border-radius: 15px; /* 10px * 1.5 */
}

.message-received {
    align-self: flex-start;
    max-width: 80%;
    display: flex;
    flex-direction: column;
}

.message-sent {
    align-self: flex-end;
    max-width: 80%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.message-content {
    padding: 15px 22px; /* 10px*1.5, 15px*1.5 */
    border-radius: 27px; /* 18px * 1.5 */
    font-size: 24px; /* 16px * 1.5 */
    line-height: 1.4;
}

.message-received .message-content {
    background-color: #2a2a2a;
    color: #e0e0e0;
    border-bottom-left-radius: 7px; /* 5px * 1.5 */
}

.message-sent .message-content {
    background-color: #007aff;
    color: #fff;
    border-bottom-right-radius: 7px; /* 5px * 1.5 */
}

.message-time {
    font-size: 16px; /* 11px * 1.5 */
    color: #a0a0a0;
    margin-top: 3px; /* 2px * 1.5 */
    margin-left: 7px; /* 5px * 1.5 */
}

.message-sent .message-time {
    margin-right: 7px; /* 5px * 1.5 */
}

/* 等待提示 */
.waiting-indicator {
    background-color: #1a1a1a;
    padding: 22px; /* 15px * 1.5 */
    border-top: 1px solid #333;
    border-bottom: 1px solid #333;
}

.waiting-time {
    display: flex;
    align-items: center;
    margin-bottom: 12px; /* 8px * 1.5 */
}

.waiting-icon {
    font-size: 30px; /* 20px * 1.5 */
    margin-right: 15px; /* 10px * 1.5 */
}

.waiting-text {
    font-size: 21px; /* 14px * 1.5 */
    color: #e0e0e0;
}

.queue-position {
    font-size: 19px; /* 13px * 1.5 */
    color: #a0a0a0;
}

/* 聊天輸入區域 */
.chat-input-area {
    height: 90px; /* 60px * 1.5 */
    background-color: #1a1a1a;
    display: flex;
    align-items: center;
    padding: 0 22px; /* 15px * 1.5 */
    border-top: 1px solid #333;
}

.chat-input-container {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #2a2a2a;
    border-radius: 30px; /* 20px * 1.5 */
    padding: 0 15px; /* 10px * 1.5 */
    margin-right: 15px; /* 10px * 1.5 */
    border: 1px solid #444;
}

.chat-attach-btn, .chat-emoji-btn {
    background: none;
    border: none;
    font-size: 30px; /* 20px * 1.5 */
    color: #a0a0a0;
    cursor: pointer;
    padding: 7px; /* 5px * 1.5 */
}

.chat-input {
    flex: 1;
    height: 54px; /* 36px * 1.5 */
    border: none;
    outline: none;
    font-size: 24px; /* 16px * 1.5 */
    padding: 0 15px; /* 10px * 1.5 */
    background-color: transparent;
    color: #e0e0e0;
}

.chat-send-btn {
    width: 54px; /* 36px * 1.5 */
    height: 54px; /* 36px * 1.5 */
    background-color: #007aff;
    border: none;
    border-radius: 50%;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.send-icon {
    font-size: 27px; /* 18px * 1.5 */
}

/* iPhone 底部指示條 */
.iphone-home-indicator {
    height: 7px; /* 5px * 1.5 */
    width: 60%; /* 40% * 1.5 */
    background-color: #444;
    border-radius: 4px; /* 3px * 1.5 */
    margin: 7px auto 12px; /* 5px*1.5, 8px*1.5 */
}

.service-chat-btn {
    background-color: #34ebd5;
    color: #333;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    margin-top: 10px;
}

.service-chat-btn:hover {
    background-color: #28b8a9;
}
