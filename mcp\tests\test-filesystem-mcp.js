// Test script for Filesystem MCP Server
// This script tests if the @modelcontextprotocol/server-filesystem package can be installed and run

const { spawn } = require('child_process');
const path = require('path');

console.log('Testing Filesystem MCP Server installation...\n');

// Test 1: Check if npx can find the package
console.log('1. Testing package availability...');
const npxPath = 'C:\\Program Files\\nodejs\\npx.cmd';
const testArgs = ['-y', '@modelcontextprotocol/server-filesystem', '--help'];

const testProcess = spawn(npxPath, testArgs, {
    stdio: ['pipe', 'pipe', 'pipe'],
    shell: true
});

let output = '';
let errorOutput = '';

testProcess.stdout.on('data', (data) => {
    output += data.toString();
});

testProcess.stderr.on('data', (data) => {
    errorOutput += data.toString();
});

testProcess.on('close', (code) => {
    console.log(`Exit code: ${code}`);
    
    if (code === 0) {
        console.log('✅ Package is available and can be installed');
        console.log('\nOutput preview:');
        console.log(output.substring(0, 500) + (output.length > 500 ? '...' : ''));
    } else {
        console.log('❌ Package installation test failed');
        console.log('Error output:', errorOutput);
    }
    
    console.log('\n2. Configuration Status:');
    console.log('✅ Added to mcp_settings.json');
    console.log('✅ Server name: github.com/modelcontextprotocol/servers/tree/main/src/filesystem');
    console.log('✅ Workspace directory: v:/xampp/htdocs/KMS_Rental_Grid.app');
    
    console.log('\n3. Next Steps:');
    console.log('- Restart VS Code or the MCP system to activate the server');
    console.log('- The server will provide filesystem tools for the workspace directory');
    console.log('- Available tools: read_file, write_file, list_directory, search_files, etc.');
});

testProcess.on('error', (error) => {
    console.log('❌ Error running test:', error.message);
});