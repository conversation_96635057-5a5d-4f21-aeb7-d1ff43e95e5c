/**
 * Modal window functionality for the KMS Rental Grid application
 */

import { getElement, showElement, hideElement, setHTML } from '../core/dom.js';
import { getMockEstateData } from '../data/mock.js';
import { getLocationState } from '../data/state.js';

// Export for circular dependency resolution
export let modalInitialized = false;

// Cache modal elements
let modalOverlay, modalImage, modalText;

/**
 * Initialize modal elements
 */
export function initializeModal() {
    modalOverlay = getElement('modalOverlay');
    modalImage = document.querySelector('.modal-image');
    modalText = getElement('modalText');

    // Add close button event listener
    const closeBtn = document.querySelector('.close-btn');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }

    modalInitialized = true;
    console.log('Modal initialized');
}

/**
 * Show the modal with estate details
 * @param {string} innerGridId - The inner grid ID
 */
export function showModal(innerGridId) {
    if (!modalOverlay || !modalImage || !modalText) {
        initializeModal();
    }

    const currentLocation = getLocationState();
    const locationId = `${currentLocation.category}-${currentLocation.gridId}-${innerGridId}`;
    console.log(`顯示模態窗口，位置ID: ${locationId}`);

    // Get estate data for this location
    const estateData = getMockEstateData(currentLocation.category, currentLocation.gridId, innerGridId);

    // Update modal title
    const titleElement = document.querySelector('.titlebar span');
    if (titleElement) {
        titleElement.textContent = estateData.name || 'Property Details';
    }

    // Show loading state for image
    if (modalImage) {
        modalImage.classList.add('loading');
        modalImage.src = '';
        modalImage.style.display = '';

        // Load the image
        const img = new Image();
        img.onload = function() {
            modalImage.src = this.src;
            modalImage.classList.remove('loading');
        };
        img.src = estateData.image;
    }

    // Update modal content
    if (modalText) {
        const content = `
            <h3>${estateData.name}</h3>
            <p>${estateData.description}</p>
            <p><strong>Owner:</strong> ${estateData.owner}</p>
            <p><strong>Price:</strong> ${estateData.price.toLocaleString()} KMS Coins</p>
            <p><strong>Location:</strong> ${locationId}</p>
            <p><strong>Updated:</strong> ${estateData.lastUpdated || new Date().toLocaleDateString('en-US')}</p>
        `;
        setHTML(modalText, content);
    }

    // Show the modal
    showElement(modalOverlay, 'flex');
}

/**
 * Show a service modal
 * @param {string} serviceType - The service type
 */
export function showServiceModal(serviceType, serviceContent) {
    if (!modalOverlay || !modalImage || !modalText) {
        initializeModal();
    }

    const service = serviceContent[serviceType];
    if (!service) {
        console.error(`未找到服務類型: ${serviceType}`);
        return;
    }

    // Update modal title
    const titleElement = document.querySelector('.titlebar span');
    if (titleElement) {
        titleElement.textContent = service.title;
    }

    // Hide image (service content doesn't need an image)
    if (modalImage) {
        modalImage.style.display = 'none';
    }

    // Show service content
    if (modalText) {
        setHTML(modalText, service.content);

        // If it's a feedback form, add submit event
        if (serviceType === 'feedback') {
            const feedbackForm = getElement('feedbackForm');
            if (feedbackForm) {
                feedbackForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    // Simulate submission
                    const successMsg = getElement('feedbackSuccess');
                    if (successMsg) {
                        successMsg.style.display = 'block';
                        feedbackForm.reset();

                        // Hide success message after 3 seconds
                        setTimeout(() => {
                            successMsg.style.display = 'none';
                        }, 3000);
                    }
                });
            }
        }
    }

    // Show the modal
    showElement(modalOverlay, 'flex');
}

/**
 * Show a premium modal
 * @param {string} premiumType - The premium type
 */
export function showPremiumModal(premiumType, premiumContent) {
    if (!modalOverlay || !modalImage || !modalText) {
        initializeModal();
    }

    const premium = premiumContent[premiumType];
    if (!premium) {
        console.error(`未找到高級類型: ${premiumType}`);
        return;
    }

    // Update modal title
    const titleElement = document.querySelector('.titlebar span');
    if (titleElement) {
        titleElement.textContent = premium.title;
    }

    // Show image if available
    if (modalImage) {
        if (premium.imageSrc) {
            modalImage.src = premium.imageSrc;
            modalImage.style.display = '';
        } else {
            modalImage.style.display = 'none';
        }
    }

    // Show premium content
    if (modalText) {
        setHTML(modalText, premium.content);
    }

    // Show the modal
    showElement(modalOverlay, 'flex');
}

/**
 * Show search results in a modal
 * @param {Object} results - The search results
 * @param {string} query - The search query
 */
export function showSearchResults(results, query) {
    if (!modalOverlay || !modalText) {
        initializeModal();
    }

    const totalResults = results.users.length + results.estates.length + results.topics.length;

    if (totalResults === 0) {
        alert(`No results found for "${query}"`);
        return;
    }

    // Prepare results HTML
    let resultHTML = `
        <div class="service-content">
            <h3>Search Results for "${query}"</h3>
    `;

    if (results.users.length > 0) {
        resultHTML += `
            <div class="service-section">
                <h4>Users (${results.users.length})</h4>
                <ul>
                    ${results.users.map(user => `<li><a href="#user-${user}">${user}</a></li>`).join('')}
                </ul>
            </div>
        `;
    }

    if (results.estates.length > 0) {
        resultHTML += `
            <div class="service-section">
                <h4>Virtual Real Estate (${results.estates.length})</h4>
                <ul>
                    ${results.estates.map(estate => `<li><a href="#estate-${estate}">${estate}</a></li>`).join('')}
                </ul>
            </div>
        `;
    }

    if (results.topics.length > 0) {
        resultHTML += `
            <div class="service-section">
                <h4>Hot Topics (${results.topics.length})</h4>
                <ul>
                    ${results.topics.map(topic => `<li><a href="#topic-${topic}">${topic}</a></li>`).join('')}
                </ul>
            </div>
        `;
    }

    resultHTML += `</div>`;

    // Update modal title
    const titleElement = document.querySelector('.titlebar span');
    if (titleElement) {
        titleElement.textContent = "Search Results";
    }

    // Hide image
    if (modalImage) {
        modalImage.style.display = 'none';
    }

    // Show results
    if (modalText) {
        setHTML(modalText, resultHTML);
    }

    // Show the modal
    showElement(modalOverlay, 'flex');
}

/**
 * Close the modal
 */
export function closeModal() {
    if (modalOverlay) {
        hideElement(modalOverlay);
    }
}
