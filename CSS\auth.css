/* ===========
   Authentication System - 認證系統樣式
   =========== */

/* Login Registration Modal Styles */
.auth-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease-out;
}

.auth-modal {
    width: 620px;
    max-width: 90%;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.15);
    overflow: hidden;
    position: relative;
    animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    transform: translateY(0);
}

.auth-header {
    background: linear-gradient(135deg, #34ebd5, #00a8cc);
    color: white;
    padding: 25px 20px;
    text-align: center;
    font-size: 26px;
    position: relative;
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.auth-close-btn {
    position: absolute;
    right: 15px;
    top: 15px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 22px;
    cursor: pointer;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.auth-close-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: rotate(90deg);
}

.auth-body {
    padding: 30px;
    max-height: 70vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #34ebd5 #f0f0f0;
}

.auth-body::-webkit-scrollbar {
    width: 8px;
}

.auth-body::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 10px;
}

.auth-body::-webkit-scrollbar-thumb {
    background: #34ebd5;
    border-radius: 10px;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
    position: relative;
    transition: all 0.3s;
}

.form-group label {
    font-weight: 500;
    font-size: 14px;
    color: #333;
    transition: color 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.form-group:focus-within label {
    color: #34ebd5;
}

.form-control {
    height: 46px;
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s;
    background-color: rgba(255, 255, 255, 0.9);
    transform-origin: left;
}

.form-control:hover {
    border-color: #ccc;
}

.form-control:focus {
    border-color: #34ebd5;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 235, 213, 0.25);
    background-color: #fff;
}

.form-group:focus-within .form-control {
    animation: pulseInput 0.3s ease-out;
}

.auth-form .error-message {
    color: #e74c3c;
    font-size: 14px;
    margin-top: 5px;
    display: none;
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

.auth-form .error-message.visible {
    display: block;
}

.auth-btn {
    height: 50px;
    background: linear-gradient(135deg, #34ebd5, #00a8cc);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 10px;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 10px rgba(52, 235, 213, 0.3);
}

.auth-btn::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 45%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0.1) 55%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(45deg);
    animation: btnShimmer 3s infinite;
    z-index: 1;
}

.auth-footer {
    padding: 20px 25px;
    text-align: center;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(0, 0, 0, 0.02);
}

.auth-footer a {
    color: #00a8cc;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s;
}

.auth-footer a:hover {
    color: #34ebd5;
    text-decoration: underline;
}

.form-row {
    display: flex;
    gap: 18px;
}

.form-row .form-group {
    flex: 1;
}

.qr-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 25px;
    padding-top: 25px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.qr-code {
    width: 160px;
    height: 160px;
    background-color: #fff;
    padding: 12px;
    border-radius: 16px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    transition: all 0.3s;
    border: 2px solid rgba(52, 235, 213, 0.3);
}

.qr-code:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.qr-code img {
    width: 100%;
    height: 100%;
}

.qr-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
}

.qr-desc {
    font-size: 14px;
    color: #666;
    text-align: center;
}

.password-wrapper {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: none;
    color: #777;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 18px;
    padding: 5px;
}

.toggle-password:hover {
    color: #34ebd5;
}

.password-requirements {
    font-size: 13px;
    color: #666;
    margin-top: 8px;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    border-left: 3px solid #ccc;
    transition: all 0.3s;
}

.password-requirements.valid {
    color: #27ae60;
    background-color: rgba(39, 174, 96, 0.05);
    border-left-color: #27ae60;
}

.password-requirements.invalid {
    color: #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
    border-left-color: #e74c3c;
}

/* 註冊表單進度指示器 */
.register-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    position: relative;
}

.register-progress::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e0e0e0;
    transform: translateY(-50%);
    z-index: 1;
}

.progress-step {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #fff;
    position: relative;
    z-index: 2;
}

.progress-step.active {
    background-color: #34ebd5;
}

.progress-step.completed {
    background-color: #27ae60;
}

/* 密碼強度指示器 */
.password-strength-meter {
    height: 5px;
    background-color: #e0e0e0;
    border-radius: 3px;
    margin-top: 8px;
    overflow: hidden;
    position: relative;
}

.password-strength-value {
    height: 100%;
    width: 0;
    border-radius: 3px;
    transition: width 0.3s, background-color 0.3s;
}

.strength-weak .password-strength-value {
    width: 25%;
    background-color: #e74c3c;
}

.strength-medium .password-strength-value {
    width: 50%;
    background-color: #f39c12;
}

.strength-strong .password-strength-value {
    width: 75%;
    background-color: #3498db;
}

.strength-very-strong .password-strength-value {
    width: 100%;
    background-color: #27ae60;
}
