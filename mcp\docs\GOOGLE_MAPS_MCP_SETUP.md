# Google Maps MCP Server Setup

## Overview
The Google Maps MCP Server provides access to Google Maps API functionality through MCP tools.

## Installation Status
✅ **COMPLETED** - Google Maps MCP server has been successfully set up

## Configuration Details

### Server Name
- **Server Name**: `github.com/modelcontextprotocol/servers/tree/main/src/google-maps`
- **Package**: `@modelcontextprotocol/server-google-maps`
- **Installation Method**: NPX

### MCP Settings Configuration
The server has been added to `mcp_settings.json` with the following configuration:

```json
{
  "github.com/modelcontextprotocol/servers/tree/main/src/google-maps": {
    "command": "C:\\Program Files\\nodejs\\npx.cmd",
    "args": [
      "-y",
      "@modelcontextprotocol/server-google-maps"
    ],
    "env": {
      "GOOGLE_MAPS_API_KEY": "YOUR_GOOGLE_MAPS_API_KEY"
    }
  }
}
```

## Available Tools

The Google Maps MCP server provides the following tools:

1. **`maps_geocode`** - Convert address to coordinates
   - Input: `address` (string)
   - Returns: location, formatted_address, place_id

2. **`maps_reverse_geocode`** - Convert coordinates to address
   - Inputs: `latitude` (number), `longitude` (number)
   - Returns: formatted_address, place_id, address_components

3. **`maps_search_places`** - Search for places using text query
   - Inputs: `query` (string), `location` (optional), `radius` (optional)
   - Returns: array of places with names, addresses, locations

4. **`maps_place_details`** - Get detailed information about a place
   - Input: `place_id` (string)
   - Returns: name, address, contact info, ratings, reviews, opening hours

5. **`maps_distance_matrix`** - Calculate distances and times between points
   - Inputs: `origins` (string[]), `destinations` (string[]), `mode` (optional)
   - Returns: distances and durations matrix

6. **`maps_elevation`** - Get elevation data for locations
   - Input: `locations` (array of {latitude, longitude})
   - Returns: elevation data for each point

7. **`maps_directions`** - Get directions between points
   - Inputs: `origin` (string), `destination` (string), `mode` (optional)
   - Returns: route details with steps, distance, duration

## API Key Setup

To use the Google Maps MCP server, you need to:

1. Get a Google Maps API key from [Google Cloud Console](https://developers.google.com/maps/documentation/javascript/get-api-key#create-api-keys)
2. Replace `YOUR_GOOGLE_MAPS_API_KEY` in `mcp_settings.json` with your actual API key
3. Ensure the following APIs are enabled in your Google Cloud project:
   - Geocoding API
   - Places API
   - Distance Matrix API
   - Elevation API
   - Directions API

## Testing

The server installation has been verified and is ready for use. Once you have a valid Google Maps API key, you can test the tools using the MCP interface.

## Directory Structure

```
mcp-servers/
└── google-maps/          # Created for Google Maps MCP server
```

## Next Steps

1. Obtain a Google Maps API key
2. Update the API key in `mcp_settings.json`
3. Test the MCP server tools through the MCP interface