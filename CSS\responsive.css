/* ===========
   Responsive Design - 響應式設計樣式
   =========== */

/* 平板設備 (768px - 1024px) */
@media (max-width: 1024px) {
    .grid-container {
        padding: 10px;
        gap: 1px;
    }

    .outer-grid-item {
        font-size: 12px;
    }

    .grid-item {
        font-size: 10px;
    }

    .toggle-button-label {
        font-size: 14px;
    }

    .title-3d {
        font-size: 32px;
    }

    .search-bar input {
        font-size: 12px;
    }

    .modal-image {
        width: 70%;
        max-height: 35vh;
    }

    #modalText h3 {
        font-size: 20px;
    }

    #modalText p {
        font-size: 14px;
    }

    /* 平板設備上的介紹頁寬度調整 */
    .guide-modal {
        width: 75vw !important;
        max-width: 900px !important;
    }
}

/* 手機設備 (最大 768px) */
@media (max-width: 768px) {
    body {
        background-attachment: scroll; /* 修復移動設備背景固定問題 */
    }
    
    .grid-container {
        padding: 5px;
        gap: 1px;
        grid-template-columns: repeat(10, 1fr); /* 減少列數 */
        grid-template-rows: repeat(15, 1fr); /* 增加行數 */
    }
    
    .outer-grid-item {
        font-size: 10px;
        border-radius: 8px;
    }
    
    .grid-item {
        font-size: 8px;
        border-radius: 4px;
    }
    
    .inner-grid {
        grid-column: 1 / span 10; /* 調整內層網格 */
        grid-row: 3 / span 12;
    }
    
    .toggle-button-label {
        font-size: 10px;
        padding: 2px 0;
    }
    
    .title-3d {
        font-size: 20px;
        letter-spacing: 1px;
    }
    
    .search-container {
        width: 95%;
        height: 28px;
    }
    
    .search-bar input {
        font-size: 10px;
        padding: 0 10px;
    }
    
    .search-bar button {
        width: 28px;
    }
    
    .search-icon {
        font-size: 14px;
    }
    
    /* 模態框響應式 */
    .win-modal {
        width: 95vw;
        height: 85vh;
        border-radius: 10px;
    }
    
    .modal-image {
        width: 90%;
        max-height: 30vh;
    }
    
    #modalText {
        width: 95%;
    }
    
    #modalText h3 {
        font-size: 18px;
    }
    
    #modalText p {
        font-size: 12px;
    }
    
    .close-btn {
        font-size: 24px;
        padding: 0 8px;
    }
    
    /* 用戶指南模態框響應式 */
    .guide-modal {
        width: 90vw;
        max-width: none;
        margin: 10px;
        border-radius: 15px;
    }
    
    .guide-header {
        padding: 15px;
        font-size: 20px;
    }
    
    .guide-body {
        padding: 20px;
    }
    
    .guide-content {
        grid-template-columns: 1fr; /* 單列布局 */
        gap: 1rem;
    }
    
    .guide-section {
        padding: 15px;
    }
    
    .guide-icon {
        font-size: 36px;
    }
    
    .guide-section h3 {
        font-size: 18px;
    }
    
    .guide-section p {
        font-size: 14px;
    }
    
    .guide-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .guide-btn {
        padding: 12px 20px;
        font-size: 16px;
        max-width: 100%;
    }
    
    /* 認證模態框響應式 */
    .auth-modal {
        width: 95%;
        margin: 10px;
    }
    
    .auth-header {
        padding: 20px 15px;
        font-size: 22px;
    }
    
    .auth-body {
        padding: 20px;
    }
    
    .form-control {
        height: 42px;
        font-size: 14px;
    }
    
    .auth-btn {
        height: 45px;
        font-size: 16px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 12px;
    }
    
    .qr-code {
        width: 120px;
        height: 120px;
    }
    
    /* Live Chat 響應式 */
    .live-chat-window {
        width: 100vw;
        height: 100vh;
        bottom: 0;
        right: 0;
        border-radius: 0;
    }
    
    .iphone-status-bar {
        height: 50px;
        padding: 0 15px;
        font-size: 16px;
    }
    
    .status-center {
        height: 35px;
        width: 180px;
    }
    
    .notch {
        width: 140px;
        height: 35px;
        border-bottom-left-radius: 20px;
        border-bottom-right-radius: 20px;
    }
    
    .chat-header {
        height: 70px;
        padding: 0 15px;
    }
    
    .chat-back-btn {
        font-size: 24px;
        margin-right: 10px;
    }
    
    .chat-avatar {
        width: 45px;
        height: 45px;
        margin-right: 10px;
    }
    
    .chat-name {
        font-size: 18px;
    }
    
    .chat-status {
        font-size: 14px;
    }
    
    .chat-messages {
        padding: 15px;
        gap: 15px;
    }
    
    .message-content {
        padding: 12px 16px;
        border-radius: 20px;
        font-size: 16px;
    }
    
    .message-time {
        font-size: 12px;
    }
    
    .chat-input-area {
        height: 70px;
        padding: 0 15px;
    }
    
    .chat-input {
        height: 40px;
        font-size: 16px;
        padding: 0 12px;
    }
    
    .chat-send-btn {
        width: 40px;
        height: 40px;
    }
    
    .send-icon {
        font-size: 20px;
    }
    
    /* 按鈕響應式 */
    .premium-icon {
        font-size: 20px;
    }
    
    .topic-icon {
        font-size: 20px;
    }
    
    .app-store-icon {
        font-size: 24px;
    }
    
    .service-icon {
        font-size: 18px;
    }
    
    .square-button {
        font-size: 12px;
    }
    
    .square-button-small {
        font-size: 12px;
    }
    
    /* 工具提示響應式 */
    #previewTooltip {
        width: 300px;
        height: 200px;
    }
    
    #tooltipInfo {
        font-size: 12px;
    }
}

/* 小型手機設備 (最大 480px) */
@media (max-width: 480px) {
    .grid-container {
        grid-template-columns: repeat(8, 1fr);
        grid-template-rows: repeat(18, 1fr);
        padding: 3px;
    }
    
    .inner-grid {
        grid-column: 1 / span 8;
        grid-row: 4 / span 14;
    }
    
    .outer-grid-item {
        font-size: 8px;
        border-radius: 6px;
    }
    
    .title-3d {
        font-size: 16px;
        letter-spacing: 0.5px;
    }
    
    .search-container {
        height: 24px;
    }
    
    .search-bar input {
        font-size: 9px;
        padding: 0 8px;
    }
    
    .search-bar button {
        width: 24px;
    }
    
    .search-icon {
        font-size: 12px;
    }
    
    .toggle-button-label {
        font-size: 8px;
        padding: 1px 0;
    }
    
    .premium-icon, .topic-icon {
        font-size: 16px;
    }
    
    .app-store-icon {
        font-size: 20px;
    }
    
    .service-icon {
        font-size: 16px;
    }
    
    /* 模態框超小屏幕調整 */
    .guide-section h3 {
        font-size: 16px;
    }
    
    .guide-section p {
        font-size: 12px;
    }
    
    .guide-btn {
        font-size: 14px;
        padding: 10px 15px;
    }
    
    .auth-header {
        font-size: 20px;
    }
    
    .form-control {
        height: 38px;
        font-size: 13px;
    }
    
    .auth-btn {
        height: 42px;
        font-size: 15px;
    }
}

/* 大屏幕設備 (1200px 以上) */
@media (min-width: 1200px) {
    .grid-container {
        padding: 20px;
        gap: 3px;
    }

    .outer-grid-item {
        font-size: 16px;
        border-radius: 16px;
    }

    .grid-item {
        font-size: 14px;
        border-radius: 10px;
    }

    .toggle-button-label {
        font-size: 18px;
    }

    .title-3d {
        font-size: 42px;
    }

    .search-bar input {
        font-size: 16px;
    }

    .premium-icon, .topic-icon {
        font-size: 28px;
    }

    .app-store-icon {
        font-size: 32px;
    }

    .service-icon {
        font-size: 26px;
    }

    /* 大屏幕上確保介紹頁為 60% 寬度 */
    .guide-modal {
        width: 60vw !important;
        max-width: 1200px !important;
    }
}

/* 超寬屏幕 (1600px 以上) */
@media (min-width: 1600px) {
    .grid-container {
        padding: 25px;
        gap: 4px;
    }
    
    .title-3d {
        font-size: 48px;
    }
    
    .toggle-button-label {
        font-size: 20px;
    }
    
    .premium-icon, .topic-icon {
        font-size: 32px;
    }
    
    .app-store-icon {
        font-size: 36px;
    }
}

/* 橫屏模式調整 */
@media (orientation: landscape) and (max-height: 600px) {
    .guide-modal {
        max-height: 90vh;
    }
    
    .guide-body {
        padding: 15px;
    }
    
    .auth-modal {
        max-height: 90vh;
    }
    
    .auth-body {
        max-height: 50vh;
    }
    
    .live-chat-window {
        height: 100vh;
    }
}

/* 高對比度模式支持 */
@media (prefers-contrast: high) {
    .grid-item {
        border-width: 2px;
    }
    
    .toggle-button {
        border: 2px solid #fff;
    }
    
    .search-bar {
        border-width: 3px;
    }
}

/* 減少動畫模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .marquee-content {
        animation: none;
    }
    
    .premium-button::before {
        animation: none;
    }
    
    .live-chat-icon {
        animation: none;
    }
}
