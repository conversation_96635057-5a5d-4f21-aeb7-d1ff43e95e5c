@echo off
echo Setting up Browser Tools MCP Server...
echo.

echo Step 1: Testing Node.js installation...
"C:\Program Files\nodejs\node.exe" --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found at expected location
    pause
    exit /b 1
)

echo.
echo Step 2: Installing Browser Tools MCP Server...
"C:\Program Files\nodejs\npx.cmd" @agentdeskai/browser-tools-mcp@latest --help

echo.
echo Step 3: MCP Configuration Status...
echo MCP server has been added to mcp_settings.json
echo Server name: github.com/AgentDeskAI/browser-tools-mcp
echo Command: C:\Program Files\nodejs\npx.cmd
echo Args: @agentdeskai/browser-tools-mcp@latest

echo.
echo Step 4: Next Steps...
echo 1. Restart your IDE to load the new MCP server configuration
echo 2. Download and install the Chrome extension from:
echo    https://github.com/AgentDeskAI/browser-tools-mcp/releases/download/v1.2.0/BrowserTools-1.2.0-extension.zip
echo 3. Start the browser tools server in a new terminal:
echo    "C:\Program Files\nodejs\npx.cmd" @agentdeskai/browser-tools-server@latest
echo 4. Open Chrome DevTools and navigate to the BrowserToolsMCP panel

echo.
echo Setup complete! Press any key to exit...
pause >nul