{"mcpServers": {"github.com/paypal/agent-toolkit": {"command": "npx", "args": ["-y", "@paypal/mcp", "--tools=all"], "env": {"PAYPAL_ACCESS_TOKEN": "YOUR_PAYPAL_ACCESS_TOKEN", "PAYPAL_ENVIRONMENT": "SANDBOX"}}, "github.com/AgentDeskAI/browser-tools-mcp": {"command": "C:\\Program Files\\nodejs\\npx.cmd", "args": ["@agentdeskai/browser-tools-mcp@latest"]}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"command": "C:\\Program Files\\nodejs\\npx.cmd", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "github.com/modelcontextprotocol/servers/tree/main/src/puppeteer": {"command": "C:\\Program Files\\nodejs\\npx.cmd", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {"command": "C:\\Program Files\\nodejs\\npx.cmd", "args": ["-y", "@modelcontextprotocol/server-filesystem", "v:/xampp/htdocs/KMS_Rental_Grid.app"]}, "github.com/modelcontextprotocol/servers/tree/main/src/google-maps": {"command": "C:\\Program Files\\nodejs\\npx.cmd", "args": ["-y", "@modelcontextprotocol/server-google-maps"], "env": {"GOOGLE_MAPS_API_KEY": "YOUR_GOOGLE_MAPS_API_KEY"}}, "github.com/NightTrek/Software-planning-mcp": {"command": "node", "args": ["v:/xampp/htdocs/KMS_Rental_Grid.app/mcp-servers/software-planning-mcp/build/index.js"], "disabled": false, "autoApprove": []}}}