/**
 * Main application initialization for the KMS Rental Grid application
 */

// Import core modules
import { getElement } from './modules/core/dom.js';
import { getRandomNumber } from './modules/core/utils.js';

// Import UI modules
import { createInnerGrid, updateContentMode, forceUpdateLocation, checkInnerGridStyles } from './modules/ui/grid.js';
import { initializeModal } from './modules/ui/modal.js';
import { initializeTooltip } from './modules/ui/tooltip.js';
import { initializeButtons } from './modules/ui/buttons.js';
import { initializeGuide, showGuideOnFirstVisit } from './modules/ui/guide.js';

// Import feature modules
import { initializeAuth } from './modules/features/auth.js';
import { initializeSearch } from './modules/features/search.js';
import { initializeLocation } from './modules/features/location.js';
import { initializeServices } from './modules/features/service.js';
import { initializePremium } from './modules/features/premium.js';
import { initializeLiveChat } from './modules/features/liveChat.js';

// Import data modules
import { resetState } from './modules/data/state.js';
import { getRandomOnlineCount } from './modules/data/mock.js';

/**
 * Initialize the application
 */
export function initializeApp() {
    console.log("Initializing application...");

    // Check if critical elements exist
    const innerGrid = getElement("innerGrid");
    if (!innerGrid) {
        console.error("Inner grid element (innerGrid) does not exist! Please check HTML structure");
        alert("Web page initialization failed: Inner grid element does not exist");
        return;
    }

    // Reset application state
    resetState();

    // Initialize UI components in the correct order
    initializeModal();
    initializeTooltip();
    initializeGuide();

    // Wait a moment to ensure modal and tooltip are initialized
    setTimeout(() => {
        initializeButtons();
    }, 100);

    // Initialize features
    initializeAuth();
    initializeSearch();
    initializeLocation();
    initializeServices();
    initializePremium();
    initializeLiveChat();

    // Set initial grid mode
    console.log("Setting initial grid mode immediately...");
    // Delay grid creation to ensure all modules are loaded
    setTimeout(() => {
        updateContentMode("1");
    }, 200);

    // Update location display
    forceUpdateLocation("A", "1", "1");

    // Update online count
    const countElement = document.querySelector(".count-number");
    if (countElement) {
        const randomCount = getRandomOnlineCount();
        countElement.textContent = randomCount;
        console.log("Online count updated:", randomCount);
    } else {
        console.error("Online count element not found");
    }

    // Update count periodically
    setInterval(() => {
        const countElement = document.querySelector(".count-number");
        if (countElement) {
            const randomCount = getRandomOnlineCount();
            countElement.textContent = randomCount;
        }
    }, 60000); // Update every minute

    // Check grid styles after a delay
    setTimeout(checkInnerGridStyles, 1000);

    // Final check to ensure grid is created
    setTimeout(() => {
        if (innerGrid && innerGrid.children.length === 0) {
            console.log("Inner grid still empty, final attempt...");
            // Force recreate grid
            updateContentMode("1");

            // If still empty, try direct DOM manipulation
            setTimeout(() => {
                if (innerGrid.children.length === 0) {
                    console.log("Force creating grid elements...");
                    for (let i = 1; i <= 36 * 20; i++) {
                        const gridItem = document.createElement('div');
                        gridItem.className = 'grid-item';
                        gridItem.dataset.landId = i.toString();
                        innerGrid.appendChild(gridItem);
                    }
                }
            }, 500);
        }
    }, 2000);

    // Show guide for first-time visitors
    setTimeout(() => {
        showGuideOnFirstVisit();
    }, 3000);

    console.log("Initialization complete");
}


