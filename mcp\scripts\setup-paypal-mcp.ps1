# PayPal Agent Toolkit MCP Server Setup Script (PowerShell)
# This script sets up the PayPal MCP server and guides through the installation process

Write-Host "Setting up PayPal Agent Toolkit MCP Server..." -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""

# Step 1: Check for Node.js installation
Write-Host "Step 1: Checking for Node.js installation..." -ForegroundColor Yellow

try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node.js is installed: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "❌ Node.js is not installed." -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Node.js first:" -ForegroundColor Yellow
    Write-Host "1. Go to https://nodejs.org/" -ForegroundColor White
    Write-Host "2. Download the LTS version for Windows" -ForegroundColor White
    Write-Host "3. Run the installer and follow the instructions" -ForegroundColor White
    Write-Host "4. Restart your PowerShell/terminal" -ForegroundColor White
    Write-Host "5. Run this script again" -ForegroundColor White
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Step 2: Test PayPal MCP package
Write-Host "Step 2: Testing PayPal Agent Toolkit MCP package..." -ForegroundColor Yellow

try {
    $output = npx -y @paypal/mcp --help 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ PayPal MCP package is available!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  PayPal MCP package installation may have issues" -ForegroundColor Yellow
        Write-Host "Output: $output" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ Failed to test PayPal MCP package" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Step 3: Check configuration
Write-Host "Step 3: Checking MCP configuration..." -ForegroundColor Yellow

if (Test-Path "mcp_settings.json") {
    Write-Host "✅ mcp_settings.json found" -ForegroundColor Green
    
    try {
        $settings = Get-Content "mcp_settings.json" | ConvertFrom-Json
        $paypalServer = $settings.mcpServers.'github.com/paypal/agent-toolkit'
        
        if ($paypalServer) {
            Write-Host "✅ PayPal server configuration found" -ForegroundColor Green
            
            $accessToken = $paypalServer.env.PAYPAL_ACCESS_TOKEN
            if ($accessToken -eq "YOUR_PAYPAL_ACCESS_TOKEN") {
                Write-Host "⚠️  Access token needs to be configured" -ForegroundColor Yellow
            } else {
                Write-Host "✅ Access token is configured" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ PayPal server not found in configuration" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error reading mcp_settings.json: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ mcp_settings.json not found" -ForegroundColor Red
}

Write-Host ""

# Step 4: Provide next steps
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "==========" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. Get PayPal API Credentials:" -ForegroundColor White
Write-Host "   - Go to https://developer.paypal.com/dashboard/" -ForegroundColor Gray
Write-Host "   - Create an app and get your Client ID and Client Secret" -ForegroundColor Gray
Write-Host ""

Write-Host "2. Generate Access Token:" -ForegroundColor White
Write-Host "   Use the following PowerShell script:" -ForegroundColor Gray
Write-Host ""
Write-Host @"
`$clientId = "YOUR_CLIENT_ID"
`$clientSecret = "YOUR_CLIENT_SECRET"
`$credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("`${clientId}:`${clientSecret}"))

`$headers = @{
    "Authorization" = "Basic `$credentials"
    "Accept" = "application/json"
    "Accept-Language" = "en_US"
}

`$body = "grant_type=client_credentials"
`$response = Invoke-RestMethod -Uri "https://api-m.sandbox.paypal.com/v1/oauth2/token" -Method Post -Headers `$headers -Body `$body -ContentType "application/x-www-form-urlencoded"
Write-Output "Access Token: `$(`$response.access_token)"
"@ -ForegroundColor DarkGray

Write-Host ""
Write-Host "3. Update mcp_settings.json:" -ForegroundColor White
Write-Host "   - Replace YOUR_PAYPAL_ACCESS_TOKEN with your actual token" -ForegroundColor Gray
Write-Host ""

Write-Host "4. Test the setup:" -ForegroundColor White
Write-Host "   - Run: node test-paypal-mcp.js" -ForegroundColor Gray
Write-Host ""

Write-Host "Available PayPal MCP Tools:" -ForegroundColor Cyan
Write-Host "- Invoice management (create, list, send, cancel)" -ForegroundColor White
Write-Host "- Payment processing (create orders, process payments)" -ForegroundColor White
Write-Host "- Dispute management" -ForegroundColor White
Write-Host "- Shipment tracking" -ForegroundColor White
Write-Host "- Catalog management" -ForegroundColor White
Write-Host "- Subscription management" -ForegroundColor White
Write-Host "- Transaction reporting" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"