/**
 * Mock data for the KMS Rental Grid application
 */

// Mock user data
export const users = [
    { id: 1, username: '<PERSON>', avatar: 'Images/avatars/user1.jpg', level: 5 },
    { id: 2, username: 'Li Xiaohua', avatar: 'Images/avatars/user2.jpg', level: 8 },
    { id: 3, username: '<PERSON>', avatar: 'Images/avatars/user3.jpg', level: 3 },
    { id: 4, username: 'KempireS Admin', avatar: 'Images/avatars/admin.jpg', level: 10 },
    { id: 5, username: 'Virtual Real Estate Expert', avatar: 'Images/avatars/expert.jpg', level: 9 }
];

// Mock estate data
export const estates = [
    { id: 'A-1-1', name: 'Luxury Villa Area A', description: 'Luxury villa in Area A with excellent views and private garden.', image: 'Images/estates/villa_a.jpg', owner: '<PERSON>', price: 1000000 },
    { id: 'B-2-3', name: 'Downtown Apartment', description: 'Modern apartment in the city center with convenient transportation.', image: 'Images/estates/apartment_b.jpg', owner: 'Li Xiaohua', price: 500000 },
    { id: 'C-3-5', name: 'Sea View B12', description: 'Luxury residence with magnificent sea views and private beach access.', image: 'Images/estates/beach_c.jpg', owner: 'Zhang Dashan', price: 1500000 },
    { id: 'D-4-7', name: 'Mountain View Villa Area C', description: 'Independent villa located on the hillside with a serene environment.', image: 'Images/estates/mountain_d.jpg', owner: 'KempireS Admin', price: 1200000 },
    { id: 'E-5-9', name: 'Business Center Area D', description: 'Office space in the business district, suitable for entrepreneurs.', image: 'Images/estates/office_e.jpg', owner: 'Virtual Real Estate Expert', price: 800000 }
];

// Mock topic data
export const topics = [
    { id: 1, name: 'Beauty Rankings', icon: '👸', popularity: 95 },
    { id: 2, name: 'Handsome Rankings', icon: '🤵', popularity: 90 },
    { id: 3, name: 'Hot Topics', icon: '🔥', popularity: 88 },
    { id: 4, name: 'Game Rankings', icon: '🎮', popularity: 85 },
    { id: 5, name: 'Food Rankings', icon: '🍔', popularity: 82 },
    { id: 6, name: 'Travel Destinations', icon: '✈️', popularity: 80 },
    { id: 7, name: 'Fashion Trends', icon: '👗', popularity: 78 },
    { id: 8, name: 'Tech News', icon: '📱', popularity: 75 }
];

// Mock search data
export const searchData = {
    users: users.map(user => user.username),
    estates: estates.map(estate => estate.name),
    topics: topics.map(topic => topic.name)
};

// Mock online user count (random between 100-9999)
export function getRandomOnlineCount() {
    return Math.floor(Math.random() * 9900) + 100;
}

// Generate mock estate data for a specific location
export function getMockEstateData(category, gridId, innerGridId) {
    const locationId = `${category}-${gridId}-${innerGridId}`;

    // Check if we have predefined data for this location
    const predefinedEstate = estates.find(estate => estate.id === locationId);
    if (predefinedEstate) {
        return predefinedEstate;
    }

    // Generate random data if no predefined data exists
    const estateTypes = ['Villa', 'Apartment', 'Commercial Space', 'Office', 'Studio', 'Garden House', 'Vacation Home'];
    const estateFeatures = ['Luxury', 'Modern', 'Classical', 'Minimalist', 'Artistic', 'Tech', 'Eco-friendly'];
    const estateViews = ['Sea View', 'Mountain View', 'City View', 'Garden View', 'Lake View', 'Forest View', 'Panoramic View'];

    const randomType = estateTypes[Math.floor(Math.random() * estateTypes.length)];
    const randomFeature = estateFeatures[Math.floor(Math.random() * estateFeatures.length)];
    const randomView = estateViews[Math.floor(Math.random() * estateViews.length)];

    const randomOwner = users[Math.floor(Math.random() * users.length)].username;
    const randomPrice = Math.floor(Math.random() * 1500000) + 100000;

    return {
        id: locationId,
        name: `${randomFeature} ${randomType} ${category}${gridId}-${innerGridId}`,
        description: `This is a ${randomFeature.toLowerCase()} style ${randomType.toLowerCase()} with excellent ${randomView.toLowerCase()}. Located in Area ${category}, Plot ${gridId}.`,
        image: `https://picsum.photos/500/300?random=${category}${gridId}${innerGridId}`,
        owner: randomOwner,
        price: randomPrice,
        lastUpdated: new Date().toLocaleDateString('en-US')
    };
}
